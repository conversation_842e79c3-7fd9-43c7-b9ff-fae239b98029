# PowerShell script untuk menjalankan semua service UNAND Chatbot
# J<PERSON><PERSON> dengan: .\start_all_services.ps1

Write-Host "========================================" -ForegroundColor Green
Write-Host "   UNAND Chatbot System - All Services" -ForegroundColor Green  
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Services yang akan dijalankan:" -ForegroundColor Yellow
Write-Host "  1. Backend FastAPI     (port 8000)" -ForegroundColor White
Write-Host "  2. Frontend React      (port 3001)" -ForegroundColor White
Write-Host "  3. Telegram <PERSON>t        (@junwar_bot)" -ForegroundColor White
Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Cek apakah Python terinstall
Write-Host "🔍 Checking Python..." -ForegroundColor Cyan
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ Python OK: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Python tidak ditemukan. Pastikan Python sudah terinstall." -ForegroundColor Red
    Write-Host "   Download dari: https://python.org" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Cek apakah Node.js terinstall
Write-Host "🔍 Checking Node.js..." -ForegroundColor Cyan
try {
    $nodeVersion = node --version 2>&1
    Write-Host "✅ Node.js OK: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js tidak ditemukan. Pastikan Node.js sudah terinstall." -ForegroundColor Red
    Write-Host "   Download dari: https://nodejs.org" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Cek apakah npm terinstall
Write-Host "🔍 Checking npm..." -ForegroundColor Cyan
try {
    $npmVersion = npm --version 2>&1
    Write-Host "✅ npm OK: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ npm tidak ditemukan. Pastikan npm sudah terinstall." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "📦 Installing dependencies..." -ForegroundColor Cyan

# Install Python dependencies
Write-Host "Installing Python dependencies..." -ForegroundColor White
Set-Location backend
try {
    pip install -r requirements.txt
    Write-Host "✅ Python dependencies installed" -ForegroundColor Green
} catch {
    Write-Host "❌ Gagal install Python dependencies" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}
Set-Location ..

# Install Node.js dependencies
Write-Host "Installing Node.js dependencies..." -ForegroundColor White
Set-Location frontend
if (-not (Test-Path "node_modules")) {
    try {
        npm install
        Write-Host "✅ Node.js dependencies installed" -ForegroundColor Green
    } catch {
        Write-Host "❌ Gagal install Node.js dependencies" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
} else {
    Write-Host "✅ Node.js dependencies already installed" -ForegroundColor Green
}
Set-Location ..

Write-Host ""
Write-Host "📋 Pastikan sebelum melanjutkan:" -ForegroundColor Yellow
Write-Host "  ✅ Database PostgreSQL sudah berjalan" -ForegroundColor White
Write-Host "  ✅ File .env sudah dikonfigurasi dengan benar" -ForegroundColor White
Write-Host "  ✅ Port 8000 dan 3001 tidak digunakan aplikasi lain" -ForegroundColor White
Write-Host ""

Read-Host "Press Enter to continue or Ctrl+C to cancel"

Write-Host ""
Write-Host "🚀 Starting all services..." -ForegroundColor Green
Write-Host ""
Write-Host "📌 Informasi penting:" -ForegroundColor Yellow
Write-Host "  - Backend akan berjalan di: http://localhost:8000" -ForegroundColor White
Write-Host "  - Frontend akan berjalan di: http://localhost:3001" -ForegroundColor White
Write-Host "  - Telegram bot akan terhubung ke @junwar_bot" -ForegroundColor White
Write-Host "  - Tekan Ctrl+C untuk menghentikan semua service" -ForegroundColor White
Write-Host ""

# Function untuk menjalankan command di background
function Start-BackgroundProcess {
    param(
        [string]$Command,
        [string]$Arguments,
        [string]$WorkingDirectory,
        [string]$Name
    )
    
    $processInfo = New-Object System.Diagnostics.ProcessStartInfo
    $processInfo.FileName = $Command
    $processInfo.Arguments = $Arguments
    $processInfo.WorkingDirectory = $WorkingDirectory
    $processInfo.UseShellExecute = $true
    $processInfo.CreateNoWindow = $false
    
    $process = [System.Diagnostics.Process]::Start($processInfo)
    Write-Host "✅ Started $Name (PID: $($process.Id))" -ForegroundColor Green
    return $process
}

try {
    # Start Backend
    Write-Host "🔧 Starting Backend FastAPI..." -ForegroundColor Cyan
    $backendProcess = Start-BackgroundProcess -Command "python" -Arguments "-m uvicorn main:app --reload --port 8000" -WorkingDirectory "backend" -Name "Backend"
    
    # Wait a bit for backend to start
    Start-Sleep -Seconds 5
    
    # Start Frontend
    Write-Host "🎨 Starting Frontend React..." -ForegroundColor Cyan
    $frontendProcess = Start-BackgroundProcess -Command "npm" -Arguments "start" -WorkingDirectory "frontend" -Name "Frontend"
    
    # Wait a bit for frontend to start
    Start-Sleep -Seconds 8
    
    # Start Telegram Bot
    Write-Host "🤖 Starting Telegram Bot..." -ForegroundColor Cyan
    $telegramProcess = Start-BackgroundProcess -Command "python" -Arguments "run_telegram_bot.py" -WorkingDirectory "." -Name "Telegram Bot"
    
    Write-Host ""
    Write-Host "🎉 All services started successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "🌐 Access URLs:" -ForegroundColor Yellow
    Write-Host "  Website: http://localhost:3001" -ForegroundColor White
    Write-Host "  Backend API: http://localhost:8000" -ForegroundColor White
    Write-Host "  API Docs: http://localhost:8000/docs" -ForegroundColor White
    Write-Host "  Telegram Bot: @junwar_bot" -ForegroundColor White
    Write-Host ""
    Write-Host "Press Enter to stop all services..." -ForegroundColor Yellow
    Read-Host
    
    # Stop all processes
    Write-Host "🛑 Stopping all services..." -ForegroundColor Red
    if ($backendProcess -and !$backendProcess.HasExited) {
        $backendProcess.Kill()
        Write-Host "✅ Backend stopped" -ForegroundColor Green
    }
    if ($frontendProcess -and !$frontendProcess.HasExited) {
        $frontendProcess.Kill()
        Write-Host "✅ Frontend stopped" -ForegroundColor Green
    }
    if ($telegramProcess -and !$telegramProcess.HasExited) {
        $telegramProcess.Kill()
        Write-Host "✅ Telegram Bot stopped" -ForegroundColor Green
    }
    
} catch {
    Write-Host "❌ Error starting services: $_" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "⏹️ All services stopped" -ForegroundColor Green
Write-Host ""
Read-Host "Press Enter to exit"

import React, { useState, useRef, useEffect } from "react";

const TelegramButton = () => {
  const [showTooltip, setShowTooltip] = useState(false);
  const [position, setPosition] = useState({ x: 16, y: 16 }); // Default: top-left (16px from edges)
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const buttonRef = useRef(null);

  const handleClick = () => {
    // Only open Telegram if not dragging
    if (!isDragging) {
      window.open("https://t.me/junwar_bot", "_blank");
    }
  };

  const handleMouseDown = (e) => {
    setIsDragging(false);
    setDragStart({
      x: e.clientX - position.x,
      y: e.clientY - position.y,
    });

    const handleMouseMove = (e) => {
      setIsDragging(true);
      const newX = Math.max(
        0,
        Math.min(window.innerWidth - 64, e.clientX - dragStart.x)
      );
      const newY = Math.max(
        0,
        Math.min(window.innerHeight - 64, e.clientY - dragStart.y)
      );
      setPosition({ x: newX, y: newY });
    };

    const handleMouseUp = () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
      // Reset dragging state after a short delay to prevent click
      setTimeout(() => setIsDragging(false), 100);
    };

    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);
  };

  const handleTouchStart = (e) => {
    setIsDragging(false);
    const touch = e.touches[0];
    setDragStart({
      x: touch.clientX - position.x,
      y: touch.clientY - position.y,
    });

    const handleTouchMove = (e) => {
      e.preventDefault();
      setIsDragging(true);
      const touch = e.touches[0];
      const newX = Math.max(
        0,
        Math.min(window.innerWidth - 64, touch.clientX - dragStart.x)
      );
      const newY = Math.max(
        0,
        Math.min(window.innerHeight - 64, touch.clientY - dragStart.y)
      );
      setPosition({ x: newX, y: newY });
    };

    const handleTouchEnd = () => {
      document.removeEventListener("touchmove", handleTouchMove);
      document.removeEventListener("touchend", handleTouchEnd);
      // Reset dragging state after a short delay to prevent click
      setTimeout(() => setIsDragging(false), 100);
    };

    document.addEventListener("touchmove", handleTouchMove, { passive: false });
    document.addEventListener("touchend", handleTouchEnd);
  };

  return (
    <div
      ref={buttonRef}
      className="fixed z-50 cursor-move"
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
      }}
    >
      {showTooltip && (
        <div className="absolute top-16 right-0 bg-gray-800 text-white text-xs md:text-sm rounded-lg py-1 px-2 md:py-2 md:px-3 shadow-lg w-40 md:w-48 text-center">
          Chat di Telegram dengan Junwar Bot
          <div className="absolute top-0 right-6 transform -translate-y-1/2 rotate-45 w-2 h-2 bg-gray-800"></div>
        </div>
      )}
      <button
        onClick={handleClick}
        onMouseDown={handleMouseDown}
        onTouchStart={handleTouchStart}
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
        className="w-14 h-14 md:w-16 md:h-16 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 active:scale-95 border-2 border-white"
        aria-label="Chat di Telegram dengan Junwar Bot"
        title="Chat di Telegram dengan Junwar Bot"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="w-7 h-7 md:w-8 md:h-8 text-white"
          viewBox="0 0 24 24"
          fill="currentColor"
          strokeWidth="0"
        >
          <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.96 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z" />
        </svg>
      </button>
    </div>
  );
};

export default TelegramButton;

@echo off
title Stop UNAND Chatbot Services
color 0C

echo ========================================
echo    Stop UNAND Chatbot Services
echo ========================================
echo.

echo 🛑 Stopping all UNAND Chatbot services...
echo.

REM Stop dengan Python script
python stop_all_services.py

echo.
echo 🔌 Killing processes on specific ports...

REM Kill process pada port 8000 (Backend)
echo Checking port 8000 (Backend)...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8000') do (
    echo Killing process %%a on port 8000
    taskkill /f /pid %%a >nul 2>&1
)

REM Kill process pada port 3001 (Frontend)
echo Checking port 3001 (Frontend)...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3001') do (
    echo Killing process %%a on port 3001
    taskkill /f /pid %%a >nul 2>&1
)

echo.
echo 🧹 Additional cleanup...

REM Kill semua process node yang mungkin masih ber<PERSON>lan
taskkill /f /im node.exe >nul 2>&1

REM Kill semua process python yang terkait
wmic process where "commandline like '%%uvicorn%%' and commandline like '%%main:app%%'" delete >nul 2>&1
wmic process where "commandline like '%%telegram_bot%%'" delete >nul 2>&1

echo.
echo ✅ All services stopped
echo.
echo You can now:
echo   - Start services again with start_all_services.bat
echo   - Check status with: python check_services_status.py
echo.
pause

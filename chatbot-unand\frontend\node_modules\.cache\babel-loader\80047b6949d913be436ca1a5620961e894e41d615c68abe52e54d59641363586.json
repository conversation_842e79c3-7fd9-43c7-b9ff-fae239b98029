{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\TelegramButton.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TelegramButton = () => {\n  _s();\n  const [showTooltip, setShowTooltip] = useState(false);\n  const handleClick = () => {\n    // Ganti dengan URL Telegram bot yang sesuai\n    window.open(\"https://t.me/junwar_bot\", \"_blank\");\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed bottom-4 right-4 md:bottom-6 md:right-6 z-40\",\n    children: [showTooltip && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute bottom-16 right-0 bg-gray-800 text-white text-xs md:text-sm rounded-lg py-1 px-2 md:py-2 md:px-3 shadow-lg w-40 md:w-48 text-center\",\n      children: [\"Chat di Telegram dengan junwar-bot\", /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-0 right-6 transform translate-y-1/2 rotate-45 w-2 h-2 bg-gray-800\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: handleClick,\n      onMouseEnter: () => setShowTooltip(true),\n      onMouseLeave: () => setShowTooltip(false),\n      onTouchStart: () => setShowTooltip(true),\n      onTouchEnd: () => {\n        setTimeout(() => setShowTooltip(false), 1500);\n      },\n      className: \"w-12 h-12 md:w-14 md:h-14 bg-gradient-to-r from-green-600 to-yellow-500 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 active:scale-95\",\n      \"aria-label\": \"Chat di Telegram dengan junwar-bot\",\n      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: \"w-6 h-6 md:w-7 md:h-7 text-white\",\n        viewBox: \"0 0 24 24\",\n        fill: \"white\",\n        strokeWidth: \"0\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.96 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_s(TelegramButton, \"MlKqB7CDspaiqeinDL2ipSY+OVU=\");\n_c = TelegramButton;\nexport default TelegramButton;\nvar _c;\n$RefreshReg$(_c, \"TelegramButton\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "TelegramButton", "_s", "showTooltip", "setShowTooltip", "handleClick", "window", "open", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onMouseEnter", "onMouseLeave", "onTouchStart", "onTouchEnd", "setTimeout", "xmlns", "viewBox", "fill", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/TelegramButton.js"], "sourcesContent": ["import React, { useState } from \"react\";\n\nconst TelegramButton = () => {\n  const [showTooltip, setShowTooltip] = useState(false);\n\n  const handleClick = () => {\n    // Ganti dengan URL Telegram bot yang sesuai\n    window.open(\"https://t.me/junwar_bot\", \"_blank\");\n  };\n\n  return (\n    <div className=\"fixed bottom-4 right-4 md:bottom-6 md:right-6 z-40\">\n      {showTooltip && (\n        <div className=\"absolute bottom-16 right-0 bg-gray-800 text-white text-xs md:text-sm rounded-lg py-1 px-2 md:py-2 md:px-3 shadow-lg w-40 md:w-48 text-center\">\n          Chat di Telegram dengan junwar-bot\n          <div className=\"absolute bottom-0 right-6 transform translate-y-1/2 rotate-45 w-2 h-2 bg-gray-800\"></div>\n        </div>\n      )}\n      <button\n        onClick={handleClick}\n        onMouseEnter={() => setShowTooltip(true)}\n        onMouseLeave={() => setShowTooltip(false)}\n        onTouchStart={() => setShowTooltip(true)}\n        onTouchEnd={() => {\n          setTimeout(() => setShowTooltip(false), 1500);\n        }}\n        className=\"w-12 h-12 md:w-14 md:h-14 bg-gradient-to-r from-green-600 to-yellow-500 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 active:scale-95\"\n        aria-label=\"Chat di Telegram dengan junwar-bot\"\n      >\n        <svg\n          xmlns=\"http://www.w3.org/2000/svg\"\n          className=\"w-6 h-6 md:w-7 md:h-7 text-white\"\n          viewBox=\"0 0 24 24\"\n          fill=\"white\"\n          strokeWidth=\"0\"\n        >\n          <path d=\"M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.96 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z\" />\n        </svg>\n      </button>\n    </div>\n  );\n};\n\nexport default TelegramButton;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGN,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAMO,WAAW,GAAGA,CAAA,KAAM;IACxB;IACAC,MAAM,CAACC,IAAI,CAAC,yBAAyB,EAAE,QAAQ,CAAC;EAClD,CAAC;EAED,oBACEP,OAAA;IAAKQ,SAAS,EAAC,oDAAoD;IAAAC,QAAA,GAChEN,WAAW,iBACVH,OAAA;MAAKQ,SAAS,EAAC,8IAA8I;MAAAC,QAAA,GAAC,oCAE5J,eAAAT,OAAA;QAAKQ,SAAS,EAAC;MAAmF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtG,CACN,eACDb,OAAA;MACEc,OAAO,EAAET,WAAY;MACrBU,YAAY,EAAEA,CAAA,KAAMX,cAAc,CAAC,IAAI,CAAE;MACzCY,YAAY,EAAEA,CAAA,KAAMZ,cAAc,CAAC,KAAK,CAAE;MAC1Ca,YAAY,EAAEA,CAAA,KAAMb,cAAc,CAAC,IAAI,CAAE;MACzCc,UAAU,EAAEA,CAAA,KAAM;QAChBC,UAAU,CAAC,MAAMf,cAAc,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;MAC/C,CAAE;MACFI,SAAS,EAAC,6MAA6M;MACvN,cAAW,oCAAoC;MAAAC,QAAA,eAE/CT,OAAA;QACEoB,KAAK,EAAC,4BAA4B;QAClCZ,SAAS,EAAC,kCAAkC;QAC5Ca,OAAO,EAAC,WAAW;QACnBC,IAAI,EAAC,OAAO;QACZC,WAAW,EAAC,GAAG;QAAAd,QAAA,eAEfT,OAAA;UAAMwB,CAAC,EAAC;QAAunB;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/nB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACX,EAAA,CAvCID,cAAc;AAAAwB,EAAA,GAAdxB,cAAc;AAyCpB,eAAeA,cAAc;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
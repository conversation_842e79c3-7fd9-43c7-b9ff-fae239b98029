{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\TelegramButton.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TelegramButton = () => {\n  _s();\n  const [showTooltip, setShowTooltip] = useState(false);\n  const [position, setPosition] = useState({\n    x: 0,\n    y: 0\n  });\n  const [isDragging, setIsDragging] = useState(false);\n  const [dragStart, setDragStart] = useState({\n    x: 0,\n    y: 0\n  });\n\n  // Set posisi default di sudut kanan atas\n  useEffect(() => {\n    const updateDefaultPosition = () => {\n      const isMobile = window.innerWidth < 768;\n      setPosition({\n        x: window.innerWidth - (isMobile ? 72 : 88),\n        // 72px untuk mobile (14*4 + 16), 88px untuk desktop (16*4 + 24)\n        y: isMobile ? 16 : 24 // top-4 untuk mobile, top-6 untuk desktop\n      });\n    };\n    updateDefaultPosition();\n    window.addEventListener(\"resize\", updateDefaultPosition);\n    return () => window.removeEventListener(\"resize\", updateDefaultPosition);\n  }, []);\n  const handleClick = () => {\n    if (!isDragging) {\n      // Ganti dengan URL Telegram bot yang sesuai\n      window.open(\"https://t.me/junwar_bot\", \"_blank\");\n    }\n  };\n  const handleMouseDown = e => {\n    setIsDragging(false);\n    setDragStart({\n      x: e.clientX - position.x,\n      y: e.clientY - position.y\n    });\n  };\n  const handleMouseMove = e => {\n    if (dragStart.x !== 0 || dragStart.y !== 0) {\n      setIsDragging(true);\n      const newX = e.clientX - dragStart.x;\n      const newY = e.clientY - dragStart.y;\n\n      // Batasi posisi agar tidak keluar dari viewport\n      const buttonSize = window.innerWidth < 768 ? 56 : 64; // 14*4 atau 16*4\n      const maxX = window.innerWidth - buttonSize;\n      const maxY = window.innerHeight - buttonSize;\n      setPosition({\n        x: Math.max(0, Math.min(newX, maxX)),\n        y: Math.max(0, Math.min(newY, maxY))\n      });\n    }\n  };\n  const handleMouseUp = () => {\n    setDragStart({\n      x: 0,\n      y: 0\n    });\n    setTimeout(() => setIsDragging(false), 100); // Delay untuk mencegah click setelah drag\n  };\n  const handleTouchStart = e => {\n    const touch = e.touches[0];\n    setIsDragging(false);\n    setDragStart({\n      x: touch.clientX - position.x,\n      y: touch.clientY - position.y\n    });\n    setShowTooltip(true);\n  };\n  const handleTouchMove = e => {\n    e.preventDefault();\n    const touch = e.touches[0];\n    if (dragStart.x !== 0 || dragStart.y !== 0) {\n      setIsDragging(true);\n      const newX = touch.clientX - dragStart.x;\n      const newY = touch.clientY - dragStart.y;\n\n      // Batasi posisi agar tidak keluar dari viewport\n      const buttonSize = window.innerWidth < 768 ? 56 : 64;\n      const maxX = window.innerWidth - buttonSize;\n      const maxY = window.innerHeight - buttonSize;\n      setPosition({\n        x: Math.max(0, Math.min(newX, maxX)),\n        y: Math.max(0, Math.min(newY, maxY))\n      });\n    }\n  };\n  const handleTouchEnd = () => {\n    setDragStart({\n      x: 0,\n      y: 0\n    });\n    if (!isDragging) {\n      setTimeout(() => setShowTooltip(false), 1500);\n    } else {\n      setShowTooltip(false);\n      setTimeout(() => setIsDragging(false), 100);\n    }\n  };\n  useEffect(() => {\n    const handleGlobalMouseMove = e => handleMouseMove(e);\n    const handleGlobalMouseUp = () => handleMouseUp();\n    if (dragStart.x !== 0 || dragStart.y !== 0) {\n      document.addEventListener(\"mousemove\", handleGlobalMouseMove);\n      document.addEventListener(\"mouseup\", handleGlobalMouseUp);\n    }\n    return () => {\n      document.removeEventListener(\"mousemove\", handleGlobalMouseMove);\n      document.removeEventListener(\"mouseup\", handleGlobalMouseUp);\n    };\n  }, [dragStart, position]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed z-50 cursor-move select-none\",\n    style: {\n      left: `${position.x}px`,\n      top: `${position.y}px`,\n      transition: isDragging ? \"none\" : \"all 0.2s ease\"\n    },\n    children: [showTooltip && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute top-16 right-0 bg-gray-800 text-white text-xs md:text-sm rounded-lg py-1 px-2 md:py-2 md:px-3 shadow-lg w-40 md:w-48 text-center\",\n      children: [\"Chat di Telegram dengan Junwar Bot\", /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-0 right-6 transform -translate-y-1/2 rotate-45 w-2 h-2 bg-gray-800\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: handleClick,\n      onMouseDown: handleMouseDown,\n      onMouseEnter: () => !isDragging && setShowTooltip(true),\n      onMouseLeave: () => setShowTooltip(false),\n      onTouchStart: handleTouchStart,\n      onTouchMove: handleTouchMove,\n      onTouchEnd: handleTouchEnd,\n      className: \"w-14 h-14 md:w-16 md:h-16 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 active:scale-95 border-2 border-white\",\n      style: {\n        cursor: isDragging ? \"grabbing\" : \"grab\",\n        userSelect: \"none\",\n        touchAction: \"none\"\n      },\n      \"aria-label\": \"Chat di Telegram dengan Junwar Bot\",\n      title: \"Chat di Telegram dengan Junwar Bot\",\n      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: \"w-7 h-7 md:w-8 md:h-8 text-white\",\n        viewBox: \"0 0 24 24\",\n        fill: \"currentColor\",\n        strokeWidth: \"0\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.96 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 118,\n    columnNumber: 5\n  }, this);\n};\n_s(TelegramButton, \"k+8NwJv4PSWYm+Y1NaLmsWQgHvo=\");\n_c = TelegramButton;\nexport default TelegramButton;\nvar _c;\n$RefreshReg$(_c, \"TelegramButton\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "jsxDEV", "_jsxDEV", "TelegramButton", "_s", "showTooltip", "setShowTooltip", "position", "setPosition", "x", "y", "isDragging", "setIsDragging", "dragStart", "setDragStart", "updateDefaultPosition", "isMobile", "window", "innerWidth", "addEventListener", "removeEventListener", "handleClick", "open", "handleMouseDown", "e", "clientX", "clientY", "handleMouseMove", "newX", "newY", "buttonSize", "maxX", "maxY", "innerHeight", "Math", "max", "min", "handleMouseUp", "setTimeout", "handleTouchStart", "touch", "touches", "handleTouchMove", "preventDefault", "handleTouchEnd", "handleGlobalMouseMove", "handleGlobalMouseUp", "document", "className", "style", "left", "top", "transition", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onMouseDown", "onMouseEnter", "onMouseLeave", "onTouchStart", "onTouchMove", "onTouchEnd", "cursor", "userSelect", "touchAction", "title", "xmlns", "viewBox", "fill", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/TelegramButton.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from \"react\";\n\nconst TelegramButton = () => {\n  const [showTooltip, setShowTooltip] = useState(false);\n  const [position, setPosition] = useState({ x: 0, y: 0 });\n  const [isDragging, setIsDragging] = useState(false);\n  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });\n\n  // Set posisi default di sudut kanan atas\n  useEffect(() => {\n    const updateDefaultPosition = () => {\n      const isMobile = window.innerWidth < 768;\n      setPosition({\n        x: window.innerWidth - (isMobile ? 72 : 88), // 72px untuk mobile (14*4 + 16), 88px untuk desktop (16*4 + 24)\n        y: isMobile ? 16 : 24, // top-4 untuk mobile, top-6 untuk desktop\n      });\n    };\n\n    updateDefaultPosition();\n    window.addEventListener(\"resize\", updateDefaultPosition);\n    return () => window.removeEventListener(\"resize\", updateDefaultPosition);\n  }, []);\n\n  const handleClick = () => {\n    if (!isDragging) {\n      // Ganti dengan URL Telegram bot yang sesuai\n      window.open(\"https://t.me/junwar_bot\", \"_blank\");\n    }\n  };\n\n  const handleMouseDown = (e) => {\n    setIsDragging(false);\n    setDragStart({\n      x: e.clientX - position.x,\n      y: e.clientY - position.y,\n    });\n  };\n\n  const handleMouseMove = (e) => {\n    if (dragStart.x !== 0 || dragStart.y !== 0) {\n      setIsDragging(true);\n      const newX = e.clientX - dragStart.x;\n      const newY = e.clientY - dragStart.y;\n\n      // Batasi posisi agar tidak keluar dari viewport\n      const buttonSize = window.innerWidth < 768 ? 56 : 64; // 14*4 atau 16*4\n      const maxX = window.innerWidth - buttonSize;\n      const maxY = window.innerHeight - buttonSize;\n\n      setPosition({\n        x: Math.max(0, Math.min(newX, maxX)),\n        y: Math.max(0, Math.min(newY, maxY)),\n      });\n    }\n  };\n\n  const handleMouseUp = () => {\n    setDragStart({ x: 0, y: 0 });\n    setTimeout(() => setIsDragging(false), 100); // Delay untuk mencegah click setelah drag\n  };\n\n  const handleTouchStart = (e) => {\n    const touch = e.touches[0];\n    setIsDragging(false);\n    setDragStart({\n      x: touch.clientX - position.x,\n      y: touch.clientY - position.y,\n    });\n    setShowTooltip(true);\n  };\n\n  const handleTouchMove = (e) => {\n    e.preventDefault();\n    const touch = e.touches[0];\n    if (dragStart.x !== 0 || dragStart.y !== 0) {\n      setIsDragging(true);\n      const newX = touch.clientX - dragStart.x;\n      const newY = touch.clientY - dragStart.y;\n\n      // Batasi posisi agar tidak keluar dari viewport\n      const buttonSize = window.innerWidth < 768 ? 56 : 64;\n      const maxX = window.innerWidth - buttonSize;\n      const maxY = window.innerHeight - buttonSize;\n\n      setPosition({\n        x: Math.max(0, Math.min(newX, maxX)),\n        y: Math.max(0, Math.min(newY, maxY)),\n      });\n    }\n  };\n\n  const handleTouchEnd = () => {\n    setDragStart({ x: 0, y: 0 });\n    if (!isDragging) {\n      setTimeout(() => setShowTooltip(false), 1500);\n    } else {\n      setShowTooltip(false);\n      setTimeout(() => setIsDragging(false), 100);\n    }\n  };\n\n  useEffect(() => {\n    const handleGlobalMouseMove = (e) => handleMouseMove(e);\n    const handleGlobalMouseUp = () => handleMouseUp();\n\n    if (dragStart.x !== 0 || dragStart.y !== 0) {\n      document.addEventListener(\"mousemove\", handleGlobalMouseMove);\n      document.addEventListener(\"mouseup\", handleGlobalMouseUp);\n    }\n\n    return () => {\n      document.removeEventListener(\"mousemove\", handleGlobalMouseMove);\n      document.removeEventListener(\"mouseup\", handleGlobalMouseUp);\n    };\n  }, [dragStart, position]);\n\n  return (\n    <div\n      className=\"fixed z-50 cursor-move select-none\"\n      style={{\n        left: `${position.x}px`,\n        top: `${position.y}px`,\n        transition: isDragging ? \"none\" : \"all 0.2s ease\",\n      }}\n    >\n      {showTooltip && (\n        <div className=\"absolute top-16 right-0 bg-gray-800 text-white text-xs md:text-sm rounded-lg py-1 px-2 md:py-2 md:px-3 shadow-lg w-40 md:w-48 text-center\">\n          Chat di Telegram dengan Junwar Bot\n          <div className=\"absolute top-0 right-6 transform -translate-y-1/2 rotate-45 w-2 h-2 bg-gray-800\"></div>\n        </div>\n      )}\n      <button\n        onClick={handleClick}\n        onMouseDown={handleMouseDown}\n        onMouseEnter={() => !isDragging && setShowTooltip(true)}\n        onMouseLeave={() => setShowTooltip(false)}\n        onTouchStart={handleTouchStart}\n        onTouchMove={handleTouchMove}\n        onTouchEnd={handleTouchEnd}\n        className=\"w-14 h-14 md:w-16 md:h-16 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 active:scale-95 border-2 border-white\"\n        style={{\n          cursor: isDragging ? \"grabbing\" : \"grab\",\n          userSelect: \"none\",\n          touchAction: \"none\",\n        }}\n        aria-label=\"Chat di Telegram dengan Junwar Bot\"\n        title=\"Chat di Telegram dengan Junwar Bot\"\n      >\n        <svg\n          xmlns=\"http://www.w3.org/2000/svg\"\n          className=\"w-7 h-7 md:w-8 md:h-8 text-white\"\n          viewBox=\"0 0 24 24\"\n          fill=\"currentColor\"\n          strokeWidth=\"0\"\n        >\n          <path d=\"M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.96 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z\" />\n        </svg>\n      </button>\n    </div>\n  );\n};\n\nexport default TelegramButton;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGR,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC;IAAEW,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EACxD,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC;IAAEW,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;;EAE1D;EACAX,SAAS,CAAC,MAAM;IACd,MAAMgB,qBAAqB,GAAGA,CAAA,KAAM;MAClC,MAAMC,QAAQ,GAAGC,MAAM,CAACC,UAAU,GAAG,GAAG;MACxCV,WAAW,CAAC;QACVC,CAAC,EAAEQ,MAAM,CAACC,UAAU,IAAIF,QAAQ,GAAG,EAAE,GAAG,EAAE,CAAC;QAAE;QAC7CN,CAAC,EAAEM,QAAQ,GAAG,EAAE,GAAG,EAAE,CAAE;MACzB,CAAC,CAAC;IACJ,CAAC;IAEDD,qBAAqB,CAAC,CAAC;IACvBE,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAEJ,qBAAqB,CAAC;IACxD,OAAO,MAAME,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEL,qBAAqB,CAAC;EAC1E,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAACV,UAAU,EAAE;MACf;MACAM,MAAM,CAACK,IAAI,CAAC,yBAAyB,EAAE,QAAQ,CAAC;IAClD;EACF,CAAC;EAED,MAAMC,eAAe,GAAIC,CAAC,IAAK;IAC7BZ,aAAa,CAAC,KAAK,CAAC;IACpBE,YAAY,CAAC;MACXL,CAAC,EAAEe,CAAC,CAACC,OAAO,GAAGlB,QAAQ,CAACE,CAAC;MACzBC,CAAC,EAAEc,CAAC,CAACE,OAAO,GAAGnB,QAAQ,CAACG;IAC1B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMiB,eAAe,GAAIH,CAAC,IAAK;IAC7B,IAAIX,SAAS,CAACJ,CAAC,KAAK,CAAC,IAAII,SAAS,CAACH,CAAC,KAAK,CAAC,EAAE;MAC1CE,aAAa,CAAC,IAAI,CAAC;MACnB,MAAMgB,IAAI,GAAGJ,CAAC,CAACC,OAAO,GAAGZ,SAAS,CAACJ,CAAC;MACpC,MAAMoB,IAAI,GAAGL,CAAC,CAACE,OAAO,GAAGb,SAAS,CAACH,CAAC;;MAEpC;MACA,MAAMoB,UAAU,GAAGb,MAAM,CAACC,UAAU,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;MACtD,MAAMa,IAAI,GAAGd,MAAM,CAACC,UAAU,GAAGY,UAAU;MAC3C,MAAME,IAAI,GAAGf,MAAM,CAACgB,WAAW,GAAGH,UAAU;MAE5CtB,WAAW,CAAC;QACVC,CAAC,EAAEyB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACR,IAAI,EAAEG,IAAI,CAAC,CAAC;QACpCrB,CAAC,EAAEwB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACP,IAAI,EAAEG,IAAI,CAAC;MACrC,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMK,aAAa,GAAGA,CAAA,KAAM;IAC1BvB,YAAY,CAAC;MAAEL,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC,CAAC;IAC5B4B,UAAU,CAAC,MAAM1B,aAAa,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EAC/C,CAAC;EAED,MAAM2B,gBAAgB,GAAIf,CAAC,IAAK;IAC9B,MAAMgB,KAAK,GAAGhB,CAAC,CAACiB,OAAO,CAAC,CAAC,CAAC;IAC1B7B,aAAa,CAAC,KAAK,CAAC;IACpBE,YAAY,CAAC;MACXL,CAAC,EAAE+B,KAAK,CAACf,OAAO,GAAGlB,QAAQ,CAACE,CAAC;MAC7BC,CAAC,EAAE8B,KAAK,CAACd,OAAO,GAAGnB,QAAQ,CAACG;IAC9B,CAAC,CAAC;IACFJ,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMoC,eAAe,GAAIlB,CAAC,IAAK;IAC7BA,CAAC,CAACmB,cAAc,CAAC,CAAC;IAClB,MAAMH,KAAK,GAAGhB,CAAC,CAACiB,OAAO,CAAC,CAAC,CAAC;IAC1B,IAAI5B,SAAS,CAACJ,CAAC,KAAK,CAAC,IAAII,SAAS,CAACH,CAAC,KAAK,CAAC,EAAE;MAC1CE,aAAa,CAAC,IAAI,CAAC;MACnB,MAAMgB,IAAI,GAAGY,KAAK,CAACf,OAAO,GAAGZ,SAAS,CAACJ,CAAC;MACxC,MAAMoB,IAAI,GAAGW,KAAK,CAACd,OAAO,GAAGb,SAAS,CAACH,CAAC;;MAExC;MACA,MAAMoB,UAAU,GAAGb,MAAM,CAACC,UAAU,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE;MACpD,MAAMa,IAAI,GAAGd,MAAM,CAACC,UAAU,GAAGY,UAAU;MAC3C,MAAME,IAAI,GAAGf,MAAM,CAACgB,WAAW,GAAGH,UAAU;MAE5CtB,WAAW,CAAC;QACVC,CAAC,EAAEyB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACR,IAAI,EAAEG,IAAI,CAAC,CAAC;QACpCrB,CAAC,EAAEwB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACP,IAAI,EAAEG,IAAI,CAAC;MACrC,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMY,cAAc,GAAGA,CAAA,KAAM;IAC3B9B,YAAY,CAAC;MAAEL,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC,CAAC;IAC5B,IAAI,CAACC,UAAU,EAAE;MACf2B,UAAU,CAAC,MAAMhC,cAAc,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IAC/C,CAAC,MAAM;MACLA,cAAc,CAAC,KAAK,CAAC;MACrBgC,UAAU,CAAC,MAAM1B,aAAa,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC;IAC7C;EACF,CAAC;EAEDb,SAAS,CAAC,MAAM;IACd,MAAM8C,qBAAqB,GAAIrB,CAAC,IAAKG,eAAe,CAACH,CAAC,CAAC;IACvD,MAAMsB,mBAAmB,GAAGA,CAAA,KAAMT,aAAa,CAAC,CAAC;IAEjD,IAAIxB,SAAS,CAACJ,CAAC,KAAK,CAAC,IAAII,SAAS,CAACH,CAAC,KAAK,CAAC,EAAE;MAC1CqC,QAAQ,CAAC5B,gBAAgB,CAAC,WAAW,EAAE0B,qBAAqB,CAAC;MAC7DE,QAAQ,CAAC5B,gBAAgB,CAAC,SAAS,EAAE2B,mBAAmB,CAAC;IAC3D;IAEA,OAAO,MAAM;MACXC,QAAQ,CAAC3B,mBAAmB,CAAC,WAAW,EAAEyB,qBAAqB,CAAC;MAChEE,QAAQ,CAAC3B,mBAAmB,CAAC,SAAS,EAAE0B,mBAAmB,CAAC;IAC9D,CAAC;EACH,CAAC,EAAE,CAACjC,SAAS,EAAEN,QAAQ,CAAC,CAAC;EAEzB,oBACEL,OAAA;IACE8C,SAAS,EAAC,oCAAoC;IAC9CC,KAAK,EAAE;MACLC,IAAI,EAAE,GAAG3C,QAAQ,CAACE,CAAC,IAAI;MACvB0C,GAAG,EAAE,GAAG5C,QAAQ,CAACG,CAAC,IAAI;MACtB0C,UAAU,EAAEzC,UAAU,GAAG,MAAM,GAAG;IACpC,CAAE;IAAA0C,QAAA,GAEDhD,WAAW,iBACVH,OAAA;MAAK8C,SAAS,EAAC,2IAA2I;MAAAK,QAAA,GAAC,oCAEzJ,eAAAnD,OAAA;QAAK8C,SAAS,EAAC;MAAiF;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpG,CACN,eACDvD,OAAA;MACEwD,OAAO,EAAErC,WAAY;MACrBsC,WAAW,EAAEpC,eAAgB;MAC7BqC,YAAY,EAAEA,CAAA,KAAM,CAACjD,UAAU,IAAIL,cAAc,CAAC,IAAI,CAAE;MACxDuD,YAAY,EAAEA,CAAA,KAAMvD,cAAc,CAAC,KAAK,CAAE;MAC1CwD,YAAY,EAAEvB,gBAAiB;MAC/BwB,WAAW,EAAErB,eAAgB;MAC7BsB,UAAU,EAAEpB,cAAe;MAC3BI,SAAS,EAAC,sQAAsQ;MAChRC,KAAK,EAAE;QACLgB,MAAM,EAAEtD,UAAU,GAAG,UAAU,GAAG,MAAM;QACxCuD,UAAU,EAAE,MAAM;QAClBC,WAAW,EAAE;MACf,CAAE;MACF,cAAW,oCAAoC;MAC/CC,KAAK,EAAC,oCAAoC;MAAAf,QAAA,eAE1CnD,OAAA;QACEmE,KAAK,EAAC,4BAA4B;QAClCrB,SAAS,EAAC,kCAAkC;QAC5CsB,OAAO,EAAC,WAAW;QACnBC,IAAI,EAAC,cAAc;QACnBC,WAAW,EAAC,GAAG;QAAAnB,QAAA,eAEfnD,OAAA;UAAMuE,CAAC,EAAC;QAAunB;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/nB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACrD,EAAA,CA9JID,cAAc;AAAAuE,EAAA,GAAdvE,cAAc;AAgKpB,eAAeA,cAAc;AAAC,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
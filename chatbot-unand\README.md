# UNAND Chatbot System

Sistem chatbot komprehensif untuk Universitas Andalas dengan 3 platform:

1. **Website Chatbot** - Interface web untuk desktop/mobile
2. **Telegram Bot** - Bot Telegram (@junwar_bot)
3. **Backend API** - Sistem RAG dengan Google Gemini AI

## 🎯 Fitur Utama

- **RAG System**: Retrieval-Augmented Generation dengan FAISS dan Google Gemini
- **Multi-Platform**: Website dan Telegram bot menggunakan backend yang sama
- **Chat History**: Riwayat percakapan tersimpan di PostgreSQL
- **Document Upload**: Upload dokumen .docx untuk basis pengetahuan
- **Session Management**: Manajemen sesi per user/platform
- **Responsive Design**: Interface yang responsif dengan branding UNAND

## 🚀 Quick Start - Jalankan Semua Service

### Opsi 1: Otomatis (Recommended)

```bash
# Windows
start_all_services.bat

# Linux/Mac
python start_all_services.py
```

### Opsi 2: Manual

```bash
# Terminal 1 - Backend
cd backend
python -m uvicorn main:app --reload

# Terminal 2 - Frontend
cd frontend
npm start

# Terminal 3 - Telegram Bot
python run_telegram_bot.py
```

## 🌐 Akses Aplikasi

Setelah menjalankan semua service:

- **Website**: http://localhost:3001
- **Backend API**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs
- **Telegram Bot**: @junwar_bot

## Struktur Proyek

```
chatbot-unand/
├── .env                  # Simpan GEMINI_API_KEY di sini
├── backend/
│   ├── main.py           # Kode FastAPI backend
│   ├── requirements.txt  # Dependensi Python
│   ├── data/             # Folder untuk menyimpan file .docx peraturan
│   │   └── peraturan_kampus.docx
│   │   └── peraturan_pemerintah.docx
│   └── vector_db/        # Folder untuk menyimpan FAISS index dan chunks
│       └── faiss_index.bin
│       └── doc_chunks.json
├── frontend/
│   ├── public/
│   │   └── index.html
│   ├── src/
│   │   ├── App.js
│   │   ├── ChatWindow.js
│   │   ├── ChatInput.js
│   │   ├── Message.js
│   │   ├── api.js
│   │   ├── index.js
│   │   └── index.css     # Untuk styling Tailwind CSS
│   ├── package.json      # Dependensi Node.js/React
│   ├── tailwind.config.js # Konfigurasi Tailwind CSS
│   ├── postcss.config.js # Konfigurasi PostCSS untuk Tailwind
│   └── .env              # Simpan REACT_APP_API_BASE_URL di sini
└── README.md
```

## Instalasi dan Menjalankan

### Backend

1. Masuk ke folder backend: `cd backend`
2. Install dependensi: `pip install -r requirements.txt`
3. Jalankan server: `python main.py`

### Frontend

1. Masuk ke folder frontend: `cd frontend`
2. Install dependensi: `npm install`
3. Jalankan aplikasi: `npm start`

## ⚙️ Environment Setup

### 1. Database Setup

```sql
CREATE DATABASE chatbot_unand_db;
```

### 2. Environment Variables

File `.env` di root project:

```env
# AI Configuration
GEMINI_API_KEY=your_gemini_api_key_here

# Database
DATABASE_URL=postgresql://postgres:password@localhost:5432/chatbot_unand_db

# Telegram Bot
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here

# Backend URL
BACKEND_URL=http://localhost:8000
```

### 3. Install Dependencies

```bash
# Python dependencies
cd backend
pip install -r requirements.txt

# Node.js dependencies
cd ../frontend
npm install
```

## 🛠️ Management Scripts

### Cek Status Semua Service

```bash
python check_services_status.py
```

### Stop Semua Service

```bash
# Windows
stop_all_services.bat

# Linux/Mac
python stop_all_services.py
```

### Test Backend Connection

```bash
python test_backend_connection.py
```

## 📱 Platform yang Tersedia

### 1. Website Chatbot

- URL: http://localhost:3001
- Fitur: Upload dokumen, chat history, responsive design
- Branding: Tema hijau-coklat UNAND dengan tagline

### 2. Telegram Bot (@junwar_bot)

- Command: `/start`, `/help`, `/reset`
- Fitur: Session per user, format response Telegram
- Integrasi: Menggunakan backend API yang sama

### 3. Backend API

- URL: http://localhost:8000
- Dokumentasi: http://localhost:8000/docs
- Fitur: RAG system, database, session management

## 📋 Persyaratan Sistem

- **Python 3.8+**
- **Node.js 16+**
- **PostgreSQL 12+**
- **Google Gemini API Key**
- **Telegram Bot Token**

@echo off
title UNAND Chatbot System - All Services
color 0A

echo ========================================
echo    UNAND Chatbot System - All Services
echo ========================================
echo.
echo Services yang akan dijalankan:
echo   1. Backend FastAPI     (port 8000)
echo   2. Frontend React      (port 3001)
echo   3. Telegram <PERSON>t        (@junwar_bot)
echo.
echo ========================================
echo.

REM Cek apakah Python terinstall
echo 🔍 Checking Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python tidak ditemukan. Pastikan Python sudah terinstall.
    echo    Download dari: https://python.org
    pause
    exit /b 1
)
echo ✅ Python OK

REM Cek apakah Node.js terinstall
echo 🔍 Checking Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js tidak ditemukan. Pastikan Node.js sudah terinstall.
    echo    Download dari: https://nodejs.org
    pause
    exit /b 1
)
echo ✅ Node.js OK

REM Cek apakah npm terinstall
echo 🔍 Checking npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm tidak ditemukan. Pastikan npm sudah terinstall.
    pause
    exit /b 1
)
echo ✅ npm OK

echo.
echo 📦 Installing dependencies...

REM Install Python dependencies
echo Installing Python dependencies...
cd backend
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ Gagal install Python dependencies
    pause
    exit /b 1
)
cd ..

REM Install Node.js dependencies
echo Installing Node.js dependencies...
cd frontend
if not exist node_modules (
    npm install
    if errorlevel 1 (
        echo ❌ Gagal install Node.js dependencies
        pause
        exit /b 1
    )
)
cd ..

echo ✅ Dependencies installed

echo.
echo 📋 Pastikan sebelum melanjutkan:
echo   ✅ Database PostgreSQL sudah berjalan
echo   ✅ File .env sudah dikonfigurasi dengan benar
echo   ✅ Port 8000 dan 3001 tidak digunakan aplikasi lain
echo.

pause

echo.
echo 🚀 Starting all services...
echo.
echo 📌 Informasi penting:
echo   - Backend akan berjalan di: http://localhost:8000
echo   - Frontend akan berjalan di: http://localhost:3001
echo   - Telegram bot akan terhubung ke @junwar_bot
echo   - Tekan Ctrl+C untuk menghentikan semua service
echo.

REM Jalankan start_all_services.py
python start_all_services.py

echo.
echo ⏹️  All services stopped
echo.
pause

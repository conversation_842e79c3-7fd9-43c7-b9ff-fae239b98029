#!/usr/bin/env python3
"""
Script untuk menjalankan backend dan telegram bot secara bersa<PERSON>
"""

import os
import sys
import time
import subprocess
import threading
from pathlib import Path

def run_backend():
    """Menjalankan backend FastAPI"""
    backend_dir = Path(__file__).parent / "backend"
    os.chdir(backend_dir)
    
    print("🚀 Starting Backend FastAPI...")
    try:
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "main:app", "--reload", "--port", "8000"
        ], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Backend failed to start: {e}")
    except KeyboardInterrupt:
        print("⏹️  Backend stopped")

def run_telegram_bot():
    """Menjalankan Telegram bot"""
    bot_dir = Path(__file__).parent
    os.chdir(bot_dir)
    
    # Tunggu backend siap (5 detik)
    print("⏳ Waiting for backend to start...")
    time.sleep(5)
    
    print("🤖 Starting Telegram Bot...")
    try:
        subprocess.run([sys.executable, "run_telegram_bot.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Telegram bot failed to start: {e}")
    except KeyboardInterrupt:
        print("⏹️  Telegram bot stopped")

def main():
    print("🎯 Starting UNAND Chatbot System")
    print("=" * 50)
    print("This will start:")
    print("  1. Backend FastAPI (port 8000)")
    print("  2. Telegram Bot")
    print("=" * 50)
    print("Press Ctrl+C to stop all services")
    print()
    
    # Jalankan backend di thread terpisah
    backend_thread = threading.Thread(target=run_backend, daemon=True)
    backend_thread.start()
    
    try:
        # Jalankan telegram bot di main thread
        run_telegram_bot()
    except KeyboardInterrupt:
        print("\n⏹️  Stopping all services...")
        sys.exit(0)

if __name__ == "__main__":
    main()

{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\TelegramButton.js\";\nimport React from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TelegramButton = () => {\n  const handleClick = () => {\n    // Ganti dengan URL Telegram bot yang sesuai\n    window.open(\"https://t.me/junwar_bot\", \"_blank\");\n  };\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    onClick: handleClick,\n    className: \"fixed bottom-6 right-6 z-40 w-14 h-14 bg-gradient-to-br from-green-600 to-yellow-600 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105\",\n    title: \"Chat di Telegram dengan junwar-bot\",\n    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n      xmlns: \"http://www.w3.org/2000/svg\",\n      className: \"w-7 h-7 text-white\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      strokeWidth: \"2\",\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\",\n      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M22 2L11 13\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M22 2L15 22L11 13L2 9L22 2Z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n};\n_c = TelegramButton;\nexport default TelegramButton;\nvar _c;\n$RefreshReg$(_c, \"TelegramButton\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "TelegramButton", "handleClick", "window", "open", "onClick", "className", "title", "children", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/TelegramButton.js"], "sourcesContent": ["import React from \"react\";\n\nconst TelegramButton = () => {\n  const handleClick = () => {\n    // Ganti dengan URL Telegram bot yang sesuai\n    window.open(\"https://t.me/junwar_bot\", \"_blank\");\n  };\n\n  return (\n    <button\n      onClick={handleClick}\n      className=\"fixed bottom-6 right-6 z-40 w-14 h-14 bg-gradient-to-br from-green-600 to-yellow-600 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105\"\n      title=\"Chat di Telegram dengan junwar-bot\"\n    >\n      <svg\n        xmlns=\"http://www.w3.org/2000/svg\"\n        className=\"w-7 h-7 text-white\"\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke=\"currentColor\"\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n      >\n        <path d=\"M22 2L11 13\"></path>\n        <path d=\"M22 2L15 22L11 13L2 9L22 2Z\"></path>\n      </svg>\n    </button>\n  );\n};\n\nexport default TelegramButton;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAC3B,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB;IACAC,MAAM,CAACC,IAAI,CAAC,yBAAyB,EAAE,QAAQ,CAAC;EAClD,CAAC;EAED,oBACEJ,OAAA;IACEK,OAAO,EAAEH,WAAY;IACrBI,SAAS,EAAC,0MAA0M;IACpNC,KAAK,EAAC,oCAAoC;IAAAC,QAAA,eAE1CR,OAAA;MACES,KAAK,EAAC,4BAA4B;MAClCH,SAAS,EAAC,oBAAoB;MAC9BI,OAAO,EAAC,WAAW;MACnBC,IAAI,EAAC,MAAM;MACXC,MAAM,EAAC,cAAc;MACrBC,WAAW,EAAC,GAAG;MACfC,aAAa,EAAC,OAAO;MACrBC,cAAc,EAAC,OAAO;MAAAP,QAAA,gBAEtBR,OAAA;QAAMgB,CAAC,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC7BpB,OAAA;QAAMgB,CAAC,EAAC;MAA6B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACC,EAAA,GA3BIpB,cAAc;AA6BpB,eAAeA,cAAc;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
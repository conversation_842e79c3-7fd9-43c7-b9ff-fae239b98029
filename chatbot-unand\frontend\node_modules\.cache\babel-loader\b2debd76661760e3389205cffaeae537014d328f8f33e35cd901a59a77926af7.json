{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\TelegramButton.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TelegramButton = () => {\n  _s();\n  const [showTooltip, setShowTooltip] = useState(false);\n  const [position, setPosition] = useState({\n    x: 16,\n    y: 16\n  }); // Default: top-left (16px from edges)\n  const [isDragging, setIsDragging] = useState(false);\n  const [dragStart, setDragStart] = useState({\n    x: 0,\n    y: 0\n  });\n  const buttonRef = useRef(null);\n  const handleClick = () => {\n    // Only open Telegram if not dragging\n    if (!isDragging) {\n      window.open(\"https://t.me/junwar_bot\", \"_blank\");\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed top-4 right-4 md:top-6 md:right-6 z-50\",\n    children: [showTooltip && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute top-16 right-0 bg-gray-800 text-white text-xs md:text-sm rounded-lg py-1 px-2 md:py-2 md:px-3 shadow-lg w-40 md:w-48 text-center\",\n      children: [\"Chat di Telegram dengan Junwar Bot\", /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-0 right-6 transform -translate-y-1/2 rotate-45 w-2 h-2 bg-gray-800\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: handleClick,\n      onMouseEnter: () => setShowTooltip(true),\n      onMouseLeave: () => setShowTooltip(false),\n      onTouchStart: () => setShowTooltip(true),\n      onTouchEnd: () => {\n        setTimeout(() => setShowTooltip(false), 1500);\n      },\n      className: \"w-14 h-14 md:w-16 md:h-16 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 active:scale-95 border-2 border-white\",\n      \"aria-label\": \"Chat di Telegram dengan Junwar Bot\",\n      title: \"Chat di Telegram dengan Junwar Bot\",\n      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: \"w-7 h-7 md:w-8 md:h-8 text-white\",\n        viewBox: \"0 0 24 24\",\n        fill: \"currentColor\",\n        strokeWidth: \"0\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.96 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this);\n};\n_s(TelegramButton, \"UQVZdrYjt11yXn3O7I9wwf/Owi8=\");\n_c = TelegramButton;\nexport default TelegramButton;\nvar _c;\n$RefreshReg$(_c, \"TelegramButton\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "jsxDEV", "_jsxDEV", "TelegramButton", "_s", "showTooltip", "setShowTooltip", "position", "setPosition", "x", "y", "isDragging", "setIsDragging", "dragStart", "setDragStart", "buttonRef", "handleClick", "window", "open", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onMouseEnter", "onMouseLeave", "onTouchStart", "onTouchEnd", "setTimeout", "title", "xmlns", "viewBox", "fill", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/TelegramButton.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from \"react\";\n\nconst TelegramButton = () => {\n  const [showTooltip, setShowTooltip] = useState(false);\n  const [position, setPosition] = useState({ x: 16, y: 16 }); // Default: top-left (16px from edges)\n  const [isDragging, setIsDragging] = useState(false);\n  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });\n  const buttonRef = useRef(null);\n\n  const handleClick = () => {\n    // Only open Telegram if not dragging\n    if (!isDragging) {\n      window.open(\"https://t.me/junwar_bot\", \"_blank\");\n    }\n  };\n\n  return (\n    <div className=\"fixed top-4 right-4 md:top-6 md:right-6 z-50\">\n      {showTooltip && (\n        <div className=\"absolute top-16 right-0 bg-gray-800 text-white text-xs md:text-sm rounded-lg py-1 px-2 md:py-2 md:px-3 shadow-lg w-40 md:w-48 text-center\">\n          Chat di Telegram dengan <PERSON>war <PERSON>t\n          <div className=\"absolute top-0 right-6 transform -translate-y-1/2 rotate-45 w-2 h-2 bg-gray-800\"></div>\n        </div>\n      )}\n      <button\n        onClick={handleClick}\n        onMouseEnter={() => setShowTooltip(true)}\n        onMouseLeave={() => setShowTooltip(false)}\n        onTouchStart={() => setShowTooltip(true)}\n        onTouchEnd={() => {\n          setTimeout(() => setShowTooltip(false), 1500);\n        }}\n        className=\"w-14 h-14 md:w-16 md:h-16 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 active:scale-95 border-2 border-white\"\n        aria-label=\"Chat di Telegram dengan Junwar Bot\"\n        title=\"Chat di Telegram dengan Junwar Bot\"\n      >\n        <svg\n          xmlns=\"http://www.w3.org/2000/svg\"\n          className=\"w-7 h-7 md:w-8 md:h-8 text-white\"\n          viewBox=\"0 0 24 24\"\n          fill=\"currentColor\"\n          strokeWidth=\"0\"\n        >\n          <path d=\"M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.96 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z\" />\n        </svg>\n      </button>\n    </div>\n  );\n};\n\nexport default TelegramButton;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGR,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC;IAAEW,CAAC,EAAE,EAAE;IAAEC,CAAC,EAAE;EAAG,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC;IAAEW,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAC1D,MAAMK,SAAS,GAAGhB,MAAM,CAAC,IAAI,CAAC;EAE9B,MAAMiB,WAAW,GAAGA,CAAA,KAAM;IACxB;IACA,IAAI,CAACL,UAAU,EAAE;MACfM,MAAM,CAACC,IAAI,CAAC,yBAAyB,EAAE,QAAQ,CAAC;IAClD;EACF,CAAC;EAED,oBACEhB,OAAA;IAAKiB,SAAS,EAAC,8CAA8C;IAAAC,QAAA,GAC1Df,WAAW,iBACVH,OAAA;MAAKiB,SAAS,EAAC,2IAA2I;MAAAC,QAAA,GAAC,oCAEzJ,eAAAlB,OAAA;QAAKiB,SAAS,EAAC;MAAiF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpG,CACN,eACDtB,OAAA;MACEuB,OAAO,EAAET,WAAY;MACrBU,YAAY,EAAEA,CAAA,KAAMpB,cAAc,CAAC,IAAI,CAAE;MACzCqB,YAAY,EAAEA,CAAA,KAAMrB,cAAc,CAAC,KAAK,CAAE;MAC1CsB,YAAY,EAAEA,CAAA,KAAMtB,cAAc,CAAC,IAAI,CAAE;MACzCuB,UAAU,EAAEA,CAAA,KAAM;QAChBC,UAAU,CAAC,MAAMxB,cAAc,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;MAC/C,CAAE;MACFa,SAAS,EAAC,sQAAsQ;MAChR,cAAW,oCAAoC;MAC/CY,KAAK,EAAC,oCAAoC;MAAAX,QAAA,eAE1ClB,OAAA;QACE8B,KAAK,EAAC,4BAA4B;QAClCb,SAAS,EAAC,kCAAkC;QAC5Cc,OAAO,EAAC,WAAW;QACnBC,IAAI,EAAC,cAAc;QACnBC,WAAW,EAAC,GAAG;QAAAf,QAAA,eAEflB,OAAA;UAAMkC,CAAC,EAAC;QAAunB;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/nB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACpB,EAAA,CA9CID,cAAc;AAAAkC,EAAA,GAAdlC,cAAc;AAgDpB,eAAeA,cAAc;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
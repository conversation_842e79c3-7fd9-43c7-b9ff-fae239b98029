{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\TelegramButton.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TelegramButton = () => {\n  _s();\n  const [showTooltip, setShowTooltip] = useState(false);\n  const [position, setPosition] = useState({\n    x: window.innerWidth - 80,\n    y: 16\n  }); // Default: top-right (16px from top, 80px from right)\n  const [isDragging, setIsDragging] = useState(false);\n  const [dragStart, setDragStart] = useState({\n    x: 0,\n    y: 0\n  });\n  const buttonRef = useRef(null);\n\n  // Handle window resize to keep button in bounds\n  useEffect(() => {\n    const handleResize = () => {\n      setPosition(prev => ({\n        x: Math.min(prev.x, window.innerWidth - 80),\n        y: Math.min(prev.y, window.innerHeight - 80)\n      }));\n    };\n    window.addEventListener(\"resize\", handleResize);\n    return () => window.removeEventListener(\"resize\", handleResize);\n  }, []);\n  const handleClick = () => {\n    // Only open Telegram if not dragging\n    if (!isDragging) {\n      window.open(\"https://t.me/junwar_bot\", \"_blank\");\n    }\n  };\n  const handleMouseDown = e => {\n    setIsDragging(false);\n    setDragStart({\n      x: e.clientX - position.x,\n      y: e.clientY - position.y\n    });\n    const handleMouseMove = e => {\n      setIsDragging(true);\n      const newX = Math.max(0, Math.min(window.innerWidth - 64, e.clientX - dragStart.x));\n      const newY = Math.max(0, Math.min(window.innerHeight - 64, e.clientY - dragStart.y));\n      setPosition({\n        x: newX,\n        y: newY\n      });\n    };\n    const handleMouseUp = () => {\n      document.removeEventListener(\"mousemove\", handleMouseMove);\n      document.removeEventListener(\"mouseup\", handleMouseUp);\n      // Reset dragging state after a short delay to prevent click\n      setTimeout(() => setIsDragging(false), 100);\n    };\n    document.addEventListener(\"mousemove\", handleMouseMove);\n    document.addEventListener(\"mouseup\", handleMouseUp);\n  };\n  const handleTouchStart = e => {\n    setIsDragging(false);\n    const touch = e.touches[0];\n    setDragStart({\n      x: touch.clientX - position.x,\n      y: touch.clientY - position.y\n    });\n    const handleTouchMove = e => {\n      e.preventDefault();\n      setIsDragging(true);\n      const touch = e.touches[0];\n      const newX = Math.max(0, Math.min(window.innerWidth - 64, touch.clientX - dragStart.x));\n      const newY = Math.max(0, Math.min(window.innerHeight - 64, touch.clientY - dragStart.y));\n      setPosition({\n        x: newX,\n        y: newY\n      });\n    };\n    const handleTouchEnd = () => {\n      document.removeEventListener(\"touchmove\", handleTouchMove);\n      document.removeEventListener(\"touchend\", handleTouchEnd);\n      // Reset dragging state after a short delay to prevent click\n      setTimeout(() => setIsDragging(false), 100);\n    };\n    document.addEventListener(\"touchmove\", handleTouchMove, {\n      passive: false\n    });\n    document.addEventListener(\"touchend\", handleTouchEnd);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: buttonRef,\n    className: \"fixed z-50 cursor-move\",\n    style: {\n      left: `${position.x}px`,\n      top: `${position.y}px`\n    },\n    children: [showTooltip && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute top-16 right-0 bg-gray-800 text-white text-xs md:text-sm rounded-lg py-1 px-2 md:py-2 md:px-3 shadow-lg w-40 md:w-48 text-center\",\n      children: [\"Chat di Telegram dengan Junwar Bot\", /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-0 right-6 transform -translate-y-1/2 rotate-45 w-2 h-2 bg-gray-800\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: handleClick,\n      onMouseDown: handleMouseDown,\n      onTouchStart: handleTouchStart,\n      onMouseEnter: () => setShowTooltip(true),\n      onMouseLeave: () => setShowTooltip(false),\n      className: \"w-14 h-14 md:w-16 md:h-16 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 active:scale-95 border-2 border-white\",\n      \"aria-label\": \"Chat di Telegram dengan Junwar Bot\",\n      title: \"Chat di Telegram dengan Junwar Bot\",\n      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: \"w-7 h-7 md:w-8 md:h-8 text-white\",\n        viewBox: \"0 0 24 24\",\n        fill: \"currentColor\",\n        strokeWidth: \"0\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.96 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 5\n  }, this);\n};\n_s(TelegramButton, \"3IPxV0Z91PImHyH1kK4yAOYTvyY=\");\n_c = TelegramButton;\nexport default TelegramButton;\nvar _c;\n$RefreshReg$(_c, \"TelegramButton\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "jsxDEV", "_jsxDEV", "TelegramButton", "_s", "showTooltip", "setShowTooltip", "position", "setPosition", "x", "window", "innerWidth", "y", "isDragging", "setIsDragging", "dragStart", "setDragStart", "buttonRef", "handleResize", "prev", "Math", "min", "innerHeight", "addEventListener", "removeEventListener", "handleClick", "open", "handleMouseDown", "e", "clientX", "clientY", "handleMouseMove", "newX", "max", "newY", "handleMouseUp", "document", "setTimeout", "handleTouchStart", "touch", "touches", "handleTouchMove", "preventDefault", "handleTouchEnd", "passive", "ref", "className", "style", "left", "top", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onMouseDown", "onTouchStart", "onMouseEnter", "onMouseLeave", "title", "xmlns", "viewBox", "fill", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/TelegramButton.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from \"react\";\n\nconst TelegramButton = () => {\n  const [showTooltip, setShowTooltip] = useState(false);\n  const [position, setPosition] = useState({\n    x: window.innerWidth - 80,\n    y: 16,\n  }); // Default: top-right (16px from top, 80px from right)\n  const [isDragging, setIsDragging] = useState(false);\n  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });\n  const buttonRef = useRef(null);\n\n  // Handle window resize to keep button in bounds\n  useEffect(() => {\n    const handleResize = () => {\n      setPosition((prev) => ({\n        x: Math.min(prev.x, window.innerWidth - 80),\n        y: Math.min(prev.y, window.innerHeight - 80),\n      }));\n    };\n\n    window.addEventListener(\"resize\", handleResize);\n    return () => window.removeEventListener(\"resize\", handleResize);\n  }, []);\n\n  const handleClick = () => {\n    // Only open Telegram if not dragging\n    if (!isDragging) {\n      window.open(\"https://t.me/junwar_bot\", \"_blank\");\n    }\n  };\n\n  const handleMouseDown = (e) => {\n    setIsDragging(false);\n    setDragStart({\n      x: e.clientX - position.x,\n      y: e.clientY - position.y,\n    });\n\n    const handleMouseMove = (e) => {\n      setIsDragging(true);\n      const newX = Math.max(\n        0,\n        Math.min(window.innerWidth - 64, e.clientX - dragStart.x)\n      );\n      const newY = Math.max(\n        0,\n        Math.min(window.innerHeight - 64, e.clientY - dragStart.y)\n      );\n      setPosition({ x: newX, y: newY });\n    };\n\n    const handleMouseUp = () => {\n      document.removeEventListener(\"mousemove\", handleMouseMove);\n      document.removeEventListener(\"mouseup\", handleMouseUp);\n      // Reset dragging state after a short delay to prevent click\n      setTimeout(() => setIsDragging(false), 100);\n    };\n\n    document.addEventListener(\"mousemove\", handleMouseMove);\n    document.addEventListener(\"mouseup\", handleMouseUp);\n  };\n\n  const handleTouchStart = (e) => {\n    setIsDragging(false);\n    const touch = e.touches[0];\n    setDragStart({\n      x: touch.clientX - position.x,\n      y: touch.clientY - position.y,\n    });\n\n    const handleTouchMove = (e) => {\n      e.preventDefault();\n      setIsDragging(true);\n      const touch = e.touches[0];\n      const newX = Math.max(\n        0,\n        Math.min(window.innerWidth - 64, touch.clientX - dragStart.x)\n      );\n      const newY = Math.max(\n        0,\n        Math.min(window.innerHeight - 64, touch.clientY - dragStart.y)\n      );\n      setPosition({ x: newX, y: newY });\n    };\n\n    const handleTouchEnd = () => {\n      document.removeEventListener(\"touchmove\", handleTouchMove);\n      document.removeEventListener(\"touchend\", handleTouchEnd);\n      // Reset dragging state after a short delay to prevent click\n      setTimeout(() => setIsDragging(false), 100);\n    };\n\n    document.addEventListener(\"touchmove\", handleTouchMove, { passive: false });\n    document.addEventListener(\"touchend\", handleTouchEnd);\n  };\n\n  return (\n    <div\n      ref={buttonRef}\n      className=\"fixed z-50 cursor-move\"\n      style={{\n        left: `${position.x}px`,\n        top: `${position.y}px`,\n      }}\n    >\n      {showTooltip && (\n        <div className=\"absolute top-16 right-0 bg-gray-800 text-white text-xs md:text-sm rounded-lg py-1 px-2 md:py-2 md:px-3 shadow-lg w-40 md:w-48 text-center\">\n          Chat di Telegram dengan Junwar Bot\n          <div className=\"absolute top-0 right-6 transform -translate-y-1/2 rotate-45 w-2 h-2 bg-gray-800\"></div>\n        </div>\n      )}\n      <button\n        onClick={handleClick}\n        onMouseDown={handleMouseDown}\n        onTouchStart={handleTouchStart}\n        onMouseEnter={() => setShowTooltip(true)}\n        onMouseLeave={() => setShowTooltip(false)}\n        className=\"w-14 h-14 md:w-16 md:h-16 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 active:scale-95 border-2 border-white\"\n        aria-label=\"Chat di Telegram dengan Junwar Bot\"\n        title=\"Chat di Telegram dengan Junwar Bot\"\n      >\n        <svg\n          xmlns=\"http://www.w3.org/2000/svg\"\n          className=\"w-7 h-7 md:w-8 md:h-8 text-white\"\n          viewBox=\"0 0 24 24\"\n          fill=\"currentColor\"\n          strokeWidth=\"0\"\n        >\n          <path d=\"M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.96 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z\" />\n        </svg>\n      </button>\n    </div>\n  );\n};\n\nexport default TelegramButton;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGR,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC;IACvCW,CAAC,EAAEC,MAAM,CAACC,UAAU,GAAG,EAAE;IACzBC,CAAC,EAAE;EACL,CAAC,CAAC,CAAC,CAAC;EACJ,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC;IAAEW,CAAC,EAAE,CAAC;IAAEG,CAAC,EAAE;EAAE,CAAC,CAAC;EAC1D,MAAMK,SAAS,GAAGlB,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACAC,SAAS,CAAC,MAAM;IACd,MAAMkB,YAAY,GAAGA,CAAA,KAAM;MACzBV,WAAW,CAAEW,IAAI,KAAM;QACrBV,CAAC,EAAEW,IAAI,CAACC,GAAG,CAACF,IAAI,CAACV,CAAC,EAAEC,MAAM,CAACC,UAAU,GAAG,EAAE,CAAC;QAC3CC,CAAC,EAAEQ,IAAI,CAACC,GAAG,CAACF,IAAI,CAACP,CAAC,EAAEF,MAAM,CAACY,WAAW,GAAG,EAAE;MAC7C,CAAC,CAAC,CAAC;IACL,CAAC;IAEDZ,MAAM,CAACa,gBAAgB,CAAC,QAAQ,EAAEL,YAAY,CAAC;IAC/C,OAAO,MAAMR,MAAM,CAACc,mBAAmB,CAAC,QAAQ,EAAEN,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMO,WAAW,GAAGA,CAAA,KAAM;IACxB;IACA,IAAI,CAACZ,UAAU,EAAE;MACfH,MAAM,CAACgB,IAAI,CAAC,yBAAyB,EAAE,QAAQ,CAAC;IAClD;EACF,CAAC;EAED,MAAMC,eAAe,GAAIC,CAAC,IAAK;IAC7Bd,aAAa,CAAC,KAAK,CAAC;IACpBE,YAAY,CAAC;MACXP,CAAC,EAAEmB,CAAC,CAACC,OAAO,GAAGtB,QAAQ,CAACE,CAAC;MACzBG,CAAC,EAAEgB,CAAC,CAACE,OAAO,GAAGvB,QAAQ,CAACK;IAC1B,CAAC,CAAC;IAEF,MAAMmB,eAAe,GAAIH,CAAC,IAAK;MAC7Bd,aAAa,CAAC,IAAI,CAAC;MACnB,MAAMkB,IAAI,GAAGZ,IAAI,CAACa,GAAG,CACnB,CAAC,EACDb,IAAI,CAACC,GAAG,CAACX,MAAM,CAACC,UAAU,GAAG,EAAE,EAAEiB,CAAC,CAACC,OAAO,GAAGd,SAAS,CAACN,CAAC,CAC1D,CAAC;MACD,MAAMyB,IAAI,GAAGd,IAAI,CAACa,GAAG,CACnB,CAAC,EACDb,IAAI,CAACC,GAAG,CAACX,MAAM,CAACY,WAAW,GAAG,EAAE,EAAEM,CAAC,CAACE,OAAO,GAAGf,SAAS,CAACH,CAAC,CAC3D,CAAC;MACDJ,WAAW,CAAC;QAAEC,CAAC,EAAEuB,IAAI;QAAEpB,CAAC,EAAEsB;MAAK,CAAC,CAAC;IACnC,CAAC;IAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;MAC1BC,QAAQ,CAACZ,mBAAmB,CAAC,WAAW,EAAEO,eAAe,CAAC;MAC1DK,QAAQ,CAACZ,mBAAmB,CAAC,SAAS,EAAEW,aAAa,CAAC;MACtD;MACAE,UAAU,CAAC,MAAMvB,aAAa,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC;IAC7C,CAAC;IAEDsB,QAAQ,CAACb,gBAAgB,CAAC,WAAW,EAAEQ,eAAe,CAAC;IACvDK,QAAQ,CAACb,gBAAgB,CAAC,SAAS,EAAEY,aAAa,CAAC;EACrD,CAAC;EAED,MAAMG,gBAAgB,GAAIV,CAAC,IAAK;IAC9Bd,aAAa,CAAC,KAAK,CAAC;IACpB,MAAMyB,KAAK,GAAGX,CAAC,CAACY,OAAO,CAAC,CAAC,CAAC;IAC1BxB,YAAY,CAAC;MACXP,CAAC,EAAE8B,KAAK,CAACV,OAAO,GAAGtB,QAAQ,CAACE,CAAC;MAC7BG,CAAC,EAAE2B,KAAK,CAACT,OAAO,GAAGvB,QAAQ,CAACK;IAC9B,CAAC,CAAC;IAEF,MAAM6B,eAAe,GAAIb,CAAC,IAAK;MAC7BA,CAAC,CAACc,cAAc,CAAC,CAAC;MAClB5B,aAAa,CAAC,IAAI,CAAC;MACnB,MAAMyB,KAAK,GAAGX,CAAC,CAACY,OAAO,CAAC,CAAC,CAAC;MAC1B,MAAMR,IAAI,GAAGZ,IAAI,CAACa,GAAG,CACnB,CAAC,EACDb,IAAI,CAACC,GAAG,CAACX,MAAM,CAACC,UAAU,GAAG,EAAE,EAAE4B,KAAK,CAACV,OAAO,GAAGd,SAAS,CAACN,CAAC,CAC9D,CAAC;MACD,MAAMyB,IAAI,GAAGd,IAAI,CAACa,GAAG,CACnB,CAAC,EACDb,IAAI,CAACC,GAAG,CAACX,MAAM,CAACY,WAAW,GAAG,EAAE,EAAEiB,KAAK,CAACT,OAAO,GAAGf,SAAS,CAACH,CAAC,CAC/D,CAAC;MACDJ,WAAW,CAAC;QAAEC,CAAC,EAAEuB,IAAI;QAAEpB,CAAC,EAAEsB;MAAK,CAAC,CAAC;IACnC,CAAC;IAED,MAAMS,cAAc,GAAGA,CAAA,KAAM;MAC3BP,QAAQ,CAACZ,mBAAmB,CAAC,WAAW,EAAEiB,eAAe,CAAC;MAC1DL,QAAQ,CAACZ,mBAAmB,CAAC,UAAU,EAAEmB,cAAc,CAAC;MACxD;MACAN,UAAU,CAAC,MAAMvB,aAAa,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC;IAC7C,CAAC;IAEDsB,QAAQ,CAACb,gBAAgB,CAAC,WAAW,EAAEkB,eAAe,EAAE;MAAEG,OAAO,EAAE;IAAM,CAAC,CAAC;IAC3ER,QAAQ,CAACb,gBAAgB,CAAC,UAAU,EAAEoB,cAAc,CAAC;EACvD,CAAC;EAED,oBACEzC,OAAA;IACE2C,GAAG,EAAE5B,SAAU;IACf6B,SAAS,EAAC,wBAAwB;IAClCC,KAAK,EAAE;MACLC,IAAI,EAAE,GAAGzC,QAAQ,CAACE,CAAC,IAAI;MACvBwC,GAAG,EAAE,GAAG1C,QAAQ,CAACK,CAAC;IACpB,CAAE;IAAAsC,QAAA,GAED7C,WAAW,iBACVH,OAAA;MAAK4C,SAAS,EAAC,2IAA2I;MAAAI,QAAA,GAAC,oCAEzJ,eAAAhD,OAAA;QAAK4C,SAAS,EAAC;MAAiF;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpG,CACN,eACDpD,OAAA;MACEqD,OAAO,EAAE9B,WAAY;MACrB+B,WAAW,EAAE7B,eAAgB;MAC7B8B,YAAY,EAAEnB,gBAAiB;MAC/BoB,YAAY,EAAEA,CAAA,KAAMpD,cAAc,CAAC,IAAI,CAAE;MACzCqD,YAAY,EAAEA,CAAA,KAAMrD,cAAc,CAAC,KAAK,CAAE;MAC1CwC,SAAS,EAAC,sQAAsQ;MAChR,cAAW,oCAAoC;MAC/Cc,KAAK,EAAC,oCAAoC;MAAAV,QAAA,eAE1ChD,OAAA;QACE2D,KAAK,EAAC,4BAA4B;QAClCf,SAAS,EAAC,kCAAkC;QAC5CgB,OAAO,EAAC,WAAW;QACnBC,IAAI,EAAC,cAAc;QACnBC,WAAW,EAAC,GAAG;QAAAd,QAAA,eAEfhD,OAAA;UAAM+D,CAAC,EAAC;QAAunB;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/nB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAClD,EAAA,CApIID,cAAc;AAAA+D,EAAA,GAAd/D,cAAc;AAsIpB,eAAeA,cAAc;AAAC,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
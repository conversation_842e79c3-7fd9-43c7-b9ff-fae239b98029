{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\TelegramButton.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TelegramButton = () => {\n  _s();\n  const [showTooltip, setShowTooltip] = useState(false);\n  const handleClick = () => {\n    // Ganti dengan URL Telegram bot yang sesuai\n    window.open(\"https://t.me/junwar_bot\", \"_blank\");\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed bottom-6 right-6 z-40\",\n    children: [showTooltip && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute bottom-16 right-0 bg-gray-800 text-white text-sm rounded-lg py-2 px-3 shadow-lg w-48 text-center\",\n      children: [\"Chat di Telegram dengan junwar-bot\", /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-0 right-6 transform translate-y-1/2 rotate-45 w-2 h-2 bg-gray-800\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: handleClick,\n      onMouseEnter: () => setShowTooltip(true),\n      onMouseLeave: () => setShowTooltip(false),\n      className: \"w-14 h-14 bg-gradient-to-br from-green-600 to-yellow-600 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105\",\n      \"aria-label\": \"Chat di Telegram dengan junwar-bot\",\n      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: \"w-7 h-7 text-white\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M22 2L11 13\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M22 2L15 22L11 13L2 9L22 2Z\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_s(TelegramButton, \"MlKqB7CDspaiqeinDL2ipSY+OVU=\");\n_c = TelegramButton;\nexport default TelegramButton;\nvar _c;\n$RefreshReg$(_c, \"TelegramButton\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "TelegramButton", "_s", "showTooltip", "setShowTooltip", "handleClick", "window", "open", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onMouseEnter", "onMouseLeave", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/TelegramButton.js"], "sourcesContent": ["import React, { useState } from \"react\";\n\nconst TelegramButton = () => {\n  const [showTooltip, setShowTooltip] = useState(false);\n\n  const handleClick = () => {\n    // Ganti dengan URL Telegram bot yang sesuai\n    window.open(\"https://t.me/junwar_bot\", \"_blank\");\n  };\n\n  return (\n    <div className=\"fixed bottom-6 right-6 z-40\">\n      {showTooltip && (\n        <div className=\"absolute bottom-16 right-0 bg-gray-800 text-white text-sm rounded-lg py-2 px-3 shadow-lg w-48 text-center\">\n          Chat di Telegram dengan junwar-bot\n          <div className=\"absolute bottom-0 right-6 transform translate-y-1/2 rotate-45 w-2 h-2 bg-gray-800\"></div>\n        </div>\n      )}\n      <button\n        onClick={handleClick}\n        onMouseEnter={() => setShowTooltip(true)}\n        onMouseLeave={() => setShowTooltip(false)}\n        className=\"w-14 h-14 bg-gradient-to-br from-green-600 to-yellow-600 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105\"\n        aria-label=\"Chat di Telegram dengan junwar-bot\"\n      >\n        <svg\n          xmlns=\"http://www.w3.org/2000/svg\"\n          className=\"w-7 h-7 text-white\"\n          viewBox=\"0 0 24 24\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          strokeWidth=\"2\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n        >\n          <path d=\"M22 2L11 13\"></path>\n          <path d=\"M22 2L15 22L11 13L2 9L22 2Z\"></path>\n        </svg>\n      </button>\n    </div>\n  );\n};\n\nexport default TelegramButton;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGN,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAMO,WAAW,GAAGA,CAAA,KAAM;IACxB;IACAC,MAAM,CAACC,IAAI,CAAC,yBAAyB,EAAE,QAAQ,CAAC;EAClD,CAAC;EAED,oBACEP,OAAA;IAAKQ,SAAS,EAAC,6BAA6B;IAAAC,QAAA,GACzCN,WAAW,iBACVH,OAAA;MAAKQ,SAAS,EAAC,2GAA2G;MAAAC,QAAA,GAAC,oCAEzH,eAAAT,OAAA;QAAKQ,SAAS,EAAC;MAAmF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtG,CACN,eACDb,OAAA;MACEc,OAAO,EAAET,WAAY;MACrBU,YAAY,EAAEA,CAAA,KAAMX,cAAc,CAAC,IAAI,CAAE;MACzCY,YAAY,EAAEA,CAAA,KAAMZ,cAAc,CAAC,KAAK,CAAE;MAC1CI,SAAS,EAAC,8KAA8K;MACxL,cAAW,oCAAoC;MAAAC,QAAA,eAE/CT,OAAA;QACEiB,KAAK,EAAC,4BAA4B;QAClCT,SAAS,EAAC,oBAAoB;QAC9BU,OAAO,EAAC,WAAW;QACnBC,IAAI,EAAC,MAAM;QACXC,MAAM,EAAC,cAAc;QACrBC,WAAW,EAAC,GAAG;QACfC,aAAa,EAAC,OAAO;QACrBC,cAAc,EAAC,OAAO;QAAAd,QAAA,gBAEtBT,OAAA;UAAMwB,CAAC,EAAC;QAAa;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC7Bb,OAAA;UAAMwB,CAAC,EAAC;QAA6B;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACX,EAAA,CAvCID,cAAc;AAAAwB,EAAA,GAAdxB,cAAc;AAyCpB,eAAeA,cAAc;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
#!/usr/bin/env python3
"""
Script untuk menjalankan semua service UNAND Chatbot:
1. Backend FastAPI (port 8000)
2. Frontend React (port 3001) 
3. Telegram Bot
"""

import os
import sys
import time
import subprocess
import threading
import signal
from pathlib import Path
import psutil

class ServiceManager:
    def __init__(self):
        self.processes = []
        self.base_dir = Path(__file__).parent
        
    def run_backend(self):
        """Menjalankan backend FastAPI"""
        backend_dir = self.base_dir / "backend"
        
        print("🚀 Starting Backend FastAPI...")
        try:
            process = subprocess.Popen([
                sys.executable, "-m", "uvicorn", 
                "main:app", "--reload", "--port", "8000", "--host", "0.0.0.0"
            ], cwd=backend_dir)
            self.processes.append(("Backend", process))
            process.wait()
        except subprocess.CalledProcessError as e:
            print(f"❌ Backend failed to start: {e}")
        except KeyboardInterrupt:
            print("⏹️  Backend stopped")

    def run_frontend(self):
        """Menjalankan frontend React"""
        frontend_dir = self.base_dir / "frontend"
        
        # Tunggu backend siap
        print("⏳ Waiting for backend to start...")
        time.sleep(3)
        
        print("🎨 Starting Frontend React...")
        try:
            # Cek apakah npm tersedia
            subprocess.run(["npm", "--version"], check=True, capture_output=True)
            
            # Install dependencies jika belum ada node_modules
            if not (frontend_dir / "node_modules").exists():
                print("📦 Installing frontend dependencies...")
                subprocess.run(["npm", "install"], cwd=frontend_dir, check=True)
            
            # Jalankan frontend
            process = subprocess.Popen([
                "npm", "start"
            ], cwd=frontend_dir)
            self.processes.append(("Frontend", process))
            process.wait()
            
        except FileNotFoundError:
            print("❌ npm not found. Please install Node.js and npm")
        except subprocess.CalledProcessError as e:
            print(f"❌ Frontend failed to start: {e}")
        except KeyboardInterrupt:
            print("⏹️  Frontend stopped")

    def run_telegram_bot(self):
        """Menjalankan Telegram bot"""
        # Tunggu backend dan frontend siap
        print("⏳ Waiting for backend and frontend to start...")
        time.sleep(8)
        
        print("🤖 Starting Telegram Bot...")
        try:
            process = subprocess.Popen([
                sys.executable, "run_telegram_bot.py"
            ], cwd=self.base_dir)
            self.processes.append(("Telegram Bot", process))
            process.wait()
        except subprocess.CalledProcessError as e:
            print(f"❌ Telegram bot failed to start: {e}")
        except KeyboardInterrupt:
            print("⏹️  Telegram bot stopped")

    def check_ports(self):
        """Cek apakah port yang diperlukan sudah digunakan"""
        ports_to_check = {
            8000: "Backend FastAPI",
            3001: "Frontend React"
        }
        
        occupied_ports = []
        for port, service in ports_to_check.items():
            for conn in psutil.net_connections():
                if conn.laddr.port == port and conn.status == 'LISTEN':
                    occupied_ports.append((port, service))
                    break
        
        if occupied_ports:
            print("⚠️  Warning: Some ports are already in use:")
            for port, service in occupied_ports:
                print(f"   Port {port} ({service}) is occupied")
            print("   Services might conflict or fail to start")
            return False
        return True

    def cleanup(self):
        """Cleanup semua process"""
        print("\n🧹 Cleaning up processes...")
        for name, process in self.processes:
            try:
                if process.poll() is None:  # Process masih berjalan
                    print(f"   Stopping {name}...")
                    process.terminate()
                    time.sleep(2)
                    if process.poll() is None:  # Masih berjalan, force kill
                        process.kill()
            except Exception as e:
                print(f"   Error stopping {name}: {e}")

    def signal_handler(self, signum, frame):
        """Handle Ctrl+C"""
        print("\n⏹️  Received stop signal...")
        self.cleanup()
        sys.exit(0)

    def start_all(self):
        """Menjalankan semua service"""
        # Setup signal handler
        signal.signal(signal.SIGINT, self.signal_handler)
        
        print("🎯 UNAND Chatbot System - Starting All Services")
        print("=" * 60)
        print("Services yang akan dijalankan:")
        print("  1. 🔧 Backend FastAPI     → http://localhost:8000")
        print("  2. 🎨 Frontend React      → http://localhost:3001") 
        print("  3. 🤖 Telegram Bot        → @junwar_bot")
        print("=" * 60)
        
        # Cek environment
        if not self.check_environment():
            return
        
        # Cek port
        self.check_ports()
        
        print("🚀 Starting services...")
        print("   Press Ctrl+C to stop all services")
        print()
        
        try:
            # Jalankan backend di thread terpisah
            backend_thread = threading.Thread(target=self.run_backend, daemon=True)
            backend_thread.start()
            
            # Jalankan frontend di thread terpisah  
            frontend_thread = threading.Thread(target=self.run_frontend, daemon=True)
            frontend_thread.start()
            
            # Jalankan telegram bot di main thread
            self.run_telegram_bot()
            
        except KeyboardInterrupt:
            self.cleanup()

    def check_environment(self):
        """Cek environment variables dan dependencies"""
        print("🔍 Checking environment...")
        
        # Cek environment variables
        required_vars = {
            "TELEGRAM_BOT_TOKEN": "Token bot Telegram",
            "GEMINI_API_KEY": "API Key Google Gemini",
            "DATABASE_URL": "URL database PostgreSQL"
        }
        
        missing_vars = []
        for var, description in required_vars.items():
            if not os.getenv(var):
                missing_vars.append(f"  ❌ {var}: {description}")
        
        if missing_vars:
            print("Environment variables berikut belum diset:")
            print("\n".join(missing_vars))
            print("\nSilakan set environment variables atau cek file .env")
            return False
        
        # Cek Python dependencies
        try:
            import fastapi, uvicorn, telegram
            print("  ✅ Python dependencies OK")
        except ImportError as e:
            print(f"  ❌ Missing Python dependency: {e}")
            print("  Run: pip install -r backend/requirements.txt")
            return False
        
        # Cek Node.js
        try:
            result = subprocess.run(["node", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"  ✅ Node.js OK ({result.stdout.strip()})")
            else:
                print("  ❌ Node.js not found")
                return False
        except FileNotFoundError:
            print("  ❌ Node.js not found. Please install Node.js")
            return False
        
        # Cek npm
        try:
            result = subprocess.run(["npm", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"  ✅ npm OK ({result.stdout.strip()})")
            else:
                print("  ❌ npm not found")
                return False
        except FileNotFoundError:
            print("  ❌ npm not found. Please install npm")
            return False
        
        print("  ✅ Environment check passed")
        return True

def main():
    manager = ServiceManager()
    manager.start_all()

if __name__ == "__main__":
    main()

import React, { useState, useEffect } from "react";
import { getSessions, deleteSession, updateSession } from "./api";

const ChatSidebar = ({
  currentSessionId,
  onSessionSelect,
  onNewChat,
  onClose,
}) => {
  const [sessions, setSessions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [editingSession, setEditingSession] = useState(null);
  const [editTitle, setEditTitle] = useState("");

  useEffect(() => {
    loadSessions();
  }, []);

  const loadSessions = async () => {
    try {
      setLoading(true);
      const sessionsData = await getSessions();
      setSessions(sessionsData);
    } catch (error) {
      console.error("Error loading sessions:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteSession = async (sessionId, e) => {
    e.stopPropagation();
    if (window.confirm("<PERSON><PERSON>kah Anda yakin ingin menghapus percakapan ini?")) {
      try {
        await deleteSession(sessionId);
        setSessions(sessions.filter((s) => s.session_id !== sessionId));
        if (currentSessionId === sessionId) {
          onNewChat();
        }
      } catch (error) {
        console.error("Error deleting session:", error);
        alert("Gagal menghapus percakapan");
      }
    }
  };

  const handleEditSession = (session, e) => {
    e.stopPropagation();
    setEditingSession(session.session_id);
    setEditTitle(session.title);
  };

  const handleSaveEdit = async (sessionId) => {
    try {
      await updateSession(sessionId, editTitle);
      setSessions(
        sessions.map((s) =>
          s.session_id === sessionId ? { ...s, title: editTitle } : s
        )
      );
      setEditingSession(null);
    } catch (error) {
      console.error("Error updating session:", error);
      alert("Gagal mengubah judul percakapan");
    }
  };

  const handleCancelEdit = () => {
    setEditingSession(null);
    setEditTitle("");
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
      return "Hari ini";
    } else if (diffDays === 2) {
      return "Kemarin";
    } else if (diffDays <= 7) {
      return `${diffDays - 1} hari lalu`;
    } else {
      return date.toLocaleDateString("id-ID", {
        day: "numeric",
        month: "short",
        year: "numeric",
      });
    }
  };

  return (
    <div className="w-80 bg-gradient-to-b from-green-50 to-yellow-50 border-r-2 border-green-600 flex flex-col h-screen">
      {/* Header */}
      <div className="p-3 border-b-2 border-green-600 bg-gradient-to-r from-green-100 to-yellow-100">
        <div className="flex items-center gap-3 mb-3">
          {/* Close button for mobile */}
          <button
            onClick={onClose}
            className="lg:hidden p-1 rounded-lg bg-green-600 text-white hover:bg-green-700 transition-colors"
          >
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>

          <div className="w-8 h-8 bg-gradient-to-br from-green-600 to-yellow-600 rounded-full flex items-center justify-center">
            <svg
              className="w-4 h-4 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <h2 className="text-lg font-semibold text-green-800">Riwayat Chat</h2>
        </div>
        <button
          onClick={onNewChat}
          className="w-full bg-gradient-to-r from-green-600 to-green-700 text-white px-4 py-2 rounded-lg hover:from-green-700 hover:to-green-800 transition-all duration-200 shadow-md hover:shadow-lg font-medium flex items-center justify-center gap-2"
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 4v16m8-8H4"
            />
          </svg>
          Chat Baru
        </button>
      </div>

      {/* Sessions List - Scrollable */}
      <div className="flex-1 overflow-y-auto">
        {loading ? (
          <div className="p-4 text-center text-green-600">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto"></div>
            <p className="mt-2">Memuat riwayat...</p>
          </div>
        ) : sessions.length === 0 ? (
          <div className="p-4 text-center text-green-600">
            <p>Belum ada percakapan</p>
            <p className="text-sm">Mulai chat baru untuk melihat riwayat</p>
          </div>
        ) : (
          <div className="p-2">
            {sessions.map((session) => (
              <div
                key={session.session_id}
                onClick={() => onSessionSelect(session.session_id)}
                className={`p-3 rounded-lg cursor-pointer transition-colors mb-2 group ${
                  currentSessionId === session.session_id
                    ? "bg-gradient-to-r from-green-100 to-yellow-100 border-2 border-green-500 shadow-md"
                    : "hover:bg-gradient-to-r hover:from-green-50 hover:to-yellow-50 border border-transparent hover:border-green-300"
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    {editingSession === session.session_id ? (
                      <div
                        className="flex items-center gap-2"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <input
                          type="text"
                          value={editTitle}
                          onChange={(e) => setEditTitle(e.target.value)}
                          className="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                          onKeyPress={(e) => {
                            if (e.key === "Enter") {
                              handleSaveEdit(session.session_id);
                            } else if (e.key === "Escape") {
                              handleCancelEdit();
                            }
                          }}
                          autoFocus
                        />
                        <button
                          onClick={() => handleSaveEdit(session.session_id)}
                          className="text-green-600 hover:text-green-700"
                        >
                          <svg
                            className="w-4 h-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        </button>
                        <button
                          onClick={handleCancelEdit}
                          className="text-gray-600 hover:text-gray-700"
                        >
                          <svg
                            className="w-4 h-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M6 18L18 6M6 6l12 12"
                            />
                          </svg>
                        </button>
                      </div>
                    ) : (
                      <>
                        <h3 className="font-medium text-green-800 truncate text-sm">
                          {session.title}
                        </h3>
                        <p className="text-xs text-green-600 mt-1">
                          {formatDate(session.updated_at)}
                        </p>
                      </>
                    )}
                  </div>

                  {editingSession !== session.session_id && (
                    <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      <button
                        onClick={(e) => handleEditSession(session, e)}
                        className="p-1 text-gray-400 hover:text-gray-600 rounded"
                        title="Edit judul"
                      >
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                          />
                        </svg>
                      </button>
                      <button
                        onClick={(e) =>
                          handleDeleteSession(session.session_id, e)
                        }
                        className="p-1 text-gray-400 hover:text-red-600 rounded"
                        title="Hapus percakapan"
                      >
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                          />
                        </svg>
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatSidebar;

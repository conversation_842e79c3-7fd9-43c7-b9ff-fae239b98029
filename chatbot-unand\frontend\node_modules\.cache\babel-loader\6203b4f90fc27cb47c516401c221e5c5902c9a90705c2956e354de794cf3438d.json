{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\TelegramButton.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TelegramButton = () => {\n  _s();\n  const [showTooltip, setShowTooltip] = useState(false);\n  const handleClick = () => {\n    // Ganti dengan URL Telegram bot yang sesuai\n    window.open(\"https://t.me/junwar_bot\", \"_blank\");\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed bottom-6 right-6 z-40\",\n    children: [showTooltip && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute bottom-16 right-0 bg-gray-800 text-white text-sm rounded-lg py-2 px-3 shadow-lg w-48 text-center\",\n      children: [\"Chat di Telegram dengan junwar-bot\", /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-0 right-6 transform translate-y-1/2 rotate-45 w-2 h-2 bg-gray-800\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: handleClick,\n      onMouseEnter: () => setShowTooltip(true),\n      onMouseLeave: () => setShowTooltip(false),\n      className: \"w-14 h-14 bg-gradient-to-r from-green-600 to-yellow-500 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105\",\n      \"aria-label\": \"Chat di Telegram dengan junwar-bot\",\n      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: \"w-7 h-7 text-white\",\n        width: \"24\",\n        height: \"24\",\n        viewBox: \"0 0 24 24\",\n        fill: \"white\",\n        strokeWidth: \"0\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.96 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_s(TelegramButton, \"MlKqB7CDspaiqeinDL2ipSY+OVU=\");\n_c = TelegramButton;\nexport default TelegramButton;\nvar _c;\n$RefreshReg$(_c, \"TelegramButton\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "TelegramButton", "_s", "showTooltip", "setShowTooltip", "handleClick", "window", "open", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onMouseEnter", "onMouseLeave", "xmlns", "width", "height", "viewBox", "fill", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/TelegramButton.js"], "sourcesContent": ["import React, { useState } from \"react\";\n\nconst TelegramButton = () => {\n  const [showTooltip, setShowTooltip] = useState(false);\n\n  const handleClick = () => {\n    // Ganti dengan URL Telegram bot yang sesuai\n    window.open(\"https://t.me/junwar_bot\", \"_blank\");\n  };\n\n  return (\n    <div className=\"fixed bottom-6 right-6 z-40\">\n      {showTooltip && (\n        <div className=\"absolute bottom-16 right-0 bg-gray-800 text-white text-sm rounded-lg py-2 px-3 shadow-lg w-48 text-center\">\n          Chat di Telegram dengan junwar-bot\n          <div className=\"absolute bottom-0 right-6 transform translate-y-1/2 rotate-45 w-2 h-2 bg-gray-800\"></div>\n        </div>\n      )}\n      <button\n        onClick={handleClick}\n        onMouseEnter={() => setShowTooltip(true)}\n        onMouseLeave={() => setShowTooltip(false)}\n        className=\"w-14 h-14 bg-gradient-to-r from-green-600 to-yellow-500 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105\"\n        aria-label=\"Chat di Telegram dengan junwar-bot\"\n      >\n        <svg\n          xmlns=\"http://www.w3.org/2000/svg\"\n          className=\"w-7 h-7 text-white\"\n          width=\"24\"\n          height=\"24\"\n          viewBox=\"0 0 24 24\"\n          fill=\"white\"\n          strokeWidth=\"0\"\n        >\n          <path d=\"M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.96 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z\" />\n        </svg>\n      </button>\n    </div>\n  );\n};\n\nexport default TelegramButton;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGN,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAMO,WAAW,GAAGA,CAAA,KAAM;IACxB;IACAC,MAAM,CAACC,IAAI,CAAC,yBAAyB,EAAE,QAAQ,CAAC;EAClD,CAAC;EAED,oBACEP,OAAA;IAAKQ,SAAS,EAAC,6BAA6B;IAAAC,QAAA,GACzCN,WAAW,iBACVH,OAAA;MAAKQ,SAAS,EAAC,2GAA2G;MAAAC,QAAA,GAAC,oCAEzH,eAAAT,OAAA;QAAKQ,SAAS,EAAC;MAAmF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtG,CACN,eACDb,OAAA;MACEc,OAAO,EAAET,WAAY;MACrBU,YAAY,EAAEA,CAAA,KAAMX,cAAc,CAAC,IAAI,CAAE;MACzCY,YAAY,EAAEA,CAAA,KAAMZ,cAAc,CAAC,KAAK,CAAE;MAC1CI,SAAS,EAAC,6KAA6K;MACvL,cAAW,oCAAoC;MAAAC,QAAA,eAE/CT,OAAA;QACEiB,KAAK,EAAC,4BAA4B;QAClCT,SAAS,EAAC,oBAAoB;QAC9BU,KAAK,EAAC,IAAI;QACVC,MAAM,EAAC,IAAI;QACXC,OAAO,EAAC,WAAW;QACnBC,IAAI,EAAC,OAAO;QACZC,WAAW,EAAC,GAAG;QAAAb,QAAA,eAEfT,OAAA;UAAMuB,CAAC,EAAC;QAAunB;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/nB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACX,EAAA,CArCID,cAAc;AAAAuB,EAAA,GAAdvB,cAAc;AAuCpB,eAAeA,cAAc;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\TelegramButton.js\";\nimport React from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TelegramButton = () => {\n  const handleClick = () => {\n    // Ganti dengan URL Telegram bot yang sesuai\n    window.open(\"https://t.me/junwar_bot\", \"_blank\");\n  };\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    onClick: handleClick,\n    className: \"fixed bottom-6 right-6 z-40 w-14 h-14 bg-gradient-to-br from-green-600 to-yellow-600 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105\",\n    title: \"Chat di Telegram\",\n    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"w-7 h-7 text-white\",\n      xmlns: \"http://www.w3.org/2000/svg\",\n      viewBox: \"0 0 24 24\",\n      fill: \"currentColor\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm0 2a10 10 0 110 20 10 10 0 010-20zm5.894 14.553l-1.431-6.744c-.175-.77-1.115-.88-1.523-.183l-4.17 7.142c-.154.264-.472.367-.736.213a.522.522 0 01-.213-.213l-1.72-2.96-.026-.032a.522.522 0 00-.713-.109l-.032.026-1.399 1.203c-.232.2-.548.174-.749-.058a.522.522 0 01-.092-.574l2.482-5.997c.176-.425.651-.628 1.076-.452.19.08.343.232.422.422l1.736 4.221 3.653-6.133c.157-.264.498-.35.762-.193a.522.522 0 01.193.193l2.482 5.997c.175.425-.027.909-.452 1.084a.832.832 0 01-.54.019z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n};\n_c = TelegramButton;\nexport default TelegramButton;\nvar _c;\n$RefreshReg$(_c, \"TelegramButton\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "TelegramButton", "handleClick", "window", "open", "onClick", "className", "title", "children", "xmlns", "viewBox", "fill", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/TelegramButton.js"], "sourcesContent": ["import React from \"react\";\n\nconst TelegramButton = () => {\n  const handleClick = () => {\n    // Ganti dengan URL Telegram bot yang sesuai\n    window.open(\"https://t.me/junwar_bot\", \"_blank\");\n  };\n\n  return (\n    <button\n      onClick={handleClick}\n      className=\"fixed bottom-6 right-6 z-40 w-14 h-14 bg-gradient-to-br from-green-600 to-yellow-600 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105\"\n      title=\"Chat di Telegram\"\n    >\n      <svg\n        className=\"w-7 h-7 text-white\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n        viewBox=\"0 0 24 24\"\n        fill=\"currentColor\"\n      >\n        <path\n          d=\"M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm0 2a10 10 0 110 20 10 10 0 010-20zm5.894 14.553l-1.431-6.744c-.175-.77-1.115-.88-1.523-.183l-4.17 7.142c-.154.264-.472.367-.736.213a.522.522 0 01-.213-.213l-1.72-2.96-.026-.032a.522.522 0 00-.713-.109l-.032.026-1.399 1.203c-.232.2-.548.174-.749-.058a.522.522 0 01-.092-.574l2.482-5.997c.176-.425.651-.628 1.076-.452.19.08.343.232.422.422l1.736 4.221 3.653-6.133c.157-.264.498-.35.762-.193a.522.522 0 01.193.193l2.482 5.997c.175.425-.027.909-.452 1.084a.832.832 0 01-.54.019z\"\n        />\n      </svg>\n    </button>\n  );\n};\n\nexport default TelegramButton;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAC3B,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB;IACAC,MAAM,CAACC,IAAI,CAAC,yBAAyB,EAAE,QAAQ,CAAC;EAClD,CAAC;EAED,oBACEJ,OAAA;IACEK,OAAO,EAAEH,WAAY;IACrBI,SAAS,EAAC,0MAA0M;IACpNC,KAAK,EAAC,kBAAkB;IAAAC,QAAA,eAExBR,OAAA;MACEM,SAAS,EAAC,oBAAoB;MAC9BG,KAAK,EAAC,4BAA4B;MAClCC,OAAO,EAAC,WAAW;MACnBC,IAAI,EAAC,cAAc;MAAAH,QAAA,eAEnBR,OAAA;QACEY,CAAC,EAAC;MAAoiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACviB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACC,EAAA,GAxBIhB,cAAc;AA0BpB,eAAeA,cAAc;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
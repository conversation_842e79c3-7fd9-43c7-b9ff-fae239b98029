{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\TelegramButton.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TelegramButton = () => {\n  _s();\n  const [showTooltip, setShowTooltip] = useState(false);\n  const [position, setPosition] = useState({\n    x: 16,\n    y: 16\n  }); // Default: top-left (16px from edges)\n  const [isDragging, setIsDragging] = useState(false);\n  const [dragStart, setDragStart] = useState({\n    x: 0,\n    y: 0\n  });\n  const buttonRef = useRef(null);\n  const handleClick = () => {\n    // Only open Telegram if not dragging\n    if (!isDragging) {\n      window.open(\"https://t.me/junwar_bot\", \"_blank\");\n    }\n  };\n  const handleMouseDown = e => {\n    setIsDragging(false);\n    setDragStart({\n      x: e.clientX - position.x,\n      y: e.clientY - position.y\n    });\n    const handleMouseMove = e => {\n      setIsDragging(true);\n      const newX = Math.max(0, Math.min(window.innerWidth - 64, e.clientX - dragStart.x));\n      const newY = Math.max(0, Math.min(window.innerHeight - 64, e.clientY - dragStart.y));\n      setPosition({\n        x: newX,\n        y: newY\n      });\n    };\n    const handleMouseUp = () => {\n      document.removeEventListener(\"mousemove\", handleMouseMove);\n      document.removeEventListener(\"mouseup\", handleMouseUp);\n      // Reset dragging state after a short delay to prevent click\n      setTimeout(() => setIsDragging(false), 100);\n    };\n    document.addEventListener(\"mousemove\", handleMouseMove);\n    document.addEventListener(\"mouseup\", handleMouseUp);\n  };\n  const handleTouchStart = e => {\n    setIsDragging(false);\n    const touch = e.touches[0];\n    setDragStart({\n      x: touch.clientX - position.x,\n      y: touch.clientY - position.y\n    });\n    const handleTouchMove = e => {\n      e.preventDefault();\n      setIsDragging(true);\n      const touch = e.touches[0];\n      const newX = Math.max(0, Math.min(window.innerWidth - 64, touch.clientX - dragStart.x));\n      const newY = Math.max(0, Math.min(window.innerHeight - 64, touch.clientY - dragStart.y));\n      setPosition({\n        x: newX,\n        y: newY\n      });\n    };\n    const handleTouchEnd = () => {\n      document.removeEventListener(\"touchmove\", handleTouchMove);\n      document.removeEventListener(\"touchend\", handleTouchEnd);\n      // Reset dragging state after a short delay to prevent click\n      setTimeout(() => setIsDragging(false), 100);\n    };\n    document.addEventListener(\"touchmove\", handleTouchMove, {\n      passive: false\n    });\n    document.addEventListener(\"touchend\", handleTouchEnd);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: buttonRef,\n    className: \"fixed z-50 cursor-move\",\n    style: {\n      left: `${position.x}px`,\n      top: `${position.y}px`\n    },\n    children: [showTooltip && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute top-16 right-0 bg-gray-800 text-white text-xs md:text-sm rounded-lg py-1 px-2 md:py-2 md:px-3 shadow-lg w-40 md:w-48 text-center\",\n      children: [\"Chat di Telegram dengan Junwar Bot\", /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-0 right-6 transform -translate-y-1/2 rotate-45 w-2 h-2 bg-gray-800\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: handleClick,\n      onMouseEnter: () => setShowTooltip(true),\n      onMouseLeave: () => setShowTooltip(false),\n      onTouchStart: () => setShowTooltip(true),\n      onTouchEnd: () => {\n        setTimeout(() => setShowTooltip(false), 1500);\n      },\n      className: \"w-14 h-14 md:w-16 md:h-16 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 active:scale-95 border-2 border-white\",\n      \"aria-label\": \"Chat di Telegram dengan Junwar Bot\",\n      title: \"Chat di Telegram dengan Junwar Bot\",\n      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: \"w-7 h-7 md:w-8 md:h-8 text-white\",\n        viewBox: \"0 0 24 24\",\n        fill: \"currentColor\",\n        strokeWidth: \"0\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.96 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 83,\n    columnNumber: 5\n  }, this);\n};\n_s(TelegramButton, \"UQVZdrYjt11yXn3O7I9wwf/Owi8=\");\n_c = TelegramButton;\nexport default TelegramButton;\nvar _c;\n$RefreshReg$(_c, \"TelegramButton\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "jsxDEV", "_jsxDEV", "TelegramButton", "_s", "showTooltip", "setShowTooltip", "position", "setPosition", "x", "y", "isDragging", "setIsDragging", "dragStart", "setDragStart", "buttonRef", "handleClick", "window", "open", "handleMouseDown", "e", "clientX", "clientY", "handleMouseMove", "newX", "Math", "max", "min", "innerWidth", "newY", "innerHeight", "handleMouseUp", "document", "removeEventListener", "setTimeout", "addEventListener", "handleTouchStart", "touch", "touches", "handleTouchMove", "preventDefault", "handleTouchEnd", "passive", "ref", "className", "style", "left", "top", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onMouseEnter", "onMouseLeave", "onTouchStart", "onTouchEnd", "title", "xmlns", "viewBox", "fill", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/TelegramButton.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from \"react\";\n\nconst TelegramButton = () => {\n  const [showTooltip, setShowTooltip] = useState(false);\n  const [position, setPosition] = useState({ x: 16, y: 16 }); // Default: top-left (16px from edges)\n  const [isDragging, setIsDragging] = useState(false);\n  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });\n  const buttonRef = useRef(null);\n\n  const handleClick = () => {\n    // Only open Telegram if not dragging\n    if (!isDragging) {\n      window.open(\"https://t.me/junwar_bot\", \"_blank\");\n    }\n  };\n\n  const handleMouseDown = (e) => {\n    setIsDragging(false);\n    setDragStart({\n      x: e.clientX - position.x,\n      y: e.clientY - position.y,\n    });\n\n    const handleMouseMove = (e) => {\n      setIsDragging(true);\n      const newX = Math.max(\n        0,\n        Math.min(window.innerWidth - 64, e.clientX - dragStart.x)\n      );\n      const newY = Math.max(\n        0,\n        Math.min(window.innerHeight - 64, e.clientY - dragStart.y)\n      );\n      setPosition({ x: newX, y: newY });\n    };\n\n    const handleMouseUp = () => {\n      document.removeEventListener(\"mousemove\", handleMouseMove);\n      document.removeEventListener(\"mouseup\", handleMouseUp);\n      // Reset dragging state after a short delay to prevent click\n      setTimeout(() => setIsDragging(false), 100);\n    };\n\n    document.addEventListener(\"mousemove\", handleMouseMove);\n    document.addEventListener(\"mouseup\", handleMouseUp);\n  };\n\n  const handleTouchStart = (e) => {\n    setIsDragging(false);\n    const touch = e.touches[0];\n    setDragStart({\n      x: touch.clientX - position.x,\n      y: touch.clientY - position.y,\n    });\n\n    const handleTouchMove = (e) => {\n      e.preventDefault();\n      setIsDragging(true);\n      const touch = e.touches[0];\n      const newX = Math.max(\n        0,\n        Math.min(window.innerWidth - 64, touch.clientX - dragStart.x)\n      );\n      const newY = Math.max(\n        0,\n        Math.min(window.innerHeight - 64, touch.clientY - dragStart.y)\n      );\n      setPosition({ x: newX, y: newY });\n    };\n\n    const handleTouchEnd = () => {\n      document.removeEventListener(\"touchmove\", handleTouchMove);\n      document.removeEventListener(\"touchend\", handleTouchEnd);\n      // Reset dragging state after a short delay to prevent click\n      setTimeout(() => setIsDragging(false), 100);\n    };\n\n    document.addEventListener(\"touchmove\", handleTouchMove, { passive: false });\n    document.addEventListener(\"touchend\", handleTouchEnd);\n  };\n\n  return (\n    <div\n      ref={buttonRef}\n      className=\"fixed z-50 cursor-move\"\n      style={{\n        left: `${position.x}px`,\n        top: `${position.y}px`,\n      }}\n    >\n      {showTooltip && (\n        <div className=\"absolute top-16 right-0 bg-gray-800 text-white text-xs md:text-sm rounded-lg py-1 px-2 md:py-2 md:px-3 shadow-lg w-40 md:w-48 text-center\">\n          Chat di Telegram dengan Junwar Bot\n          <div className=\"absolute top-0 right-6 transform -translate-y-1/2 rotate-45 w-2 h-2 bg-gray-800\"></div>\n        </div>\n      )}\n      <button\n        onClick={handleClick}\n        onMouseEnter={() => setShowTooltip(true)}\n        onMouseLeave={() => setShowTooltip(false)}\n        onTouchStart={() => setShowTooltip(true)}\n        onTouchEnd={() => {\n          setTimeout(() => setShowTooltip(false), 1500);\n        }}\n        className=\"w-14 h-14 md:w-16 md:h-16 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 active:scale-95 border-2 border-white\"\n        aria-label=\"Chat di Telegram dengan Junwar Bot\"\n        title=\"Chat di Telegram dengan Junwar Bot\"\n      >\n        <svg\n          xmlns=\"http://www.w3.org/2000/svg\"\n          className=\"w-7 h-7 md:w-8 md:h-8 text-white\"\n          viewBox=\"0 0 24 24\"\n          fill=\"currentColor\"\n          strokeWidth=\"0\"\n        >\n          <path d=\"M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.96 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z\" />\n        </svg>\n      </button>\n    </div>\n  );\n};\n\nexport default TelegramButton;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGR,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC;IAAEW,CAAC,EAAE,EAAE;IAAEC,CAAC,EAAE;EAAG,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC;IAAEW,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAC1D,MAAMK,SAAS,GAAGhB,MAAM,CAAC,IAAI,CAAC;EAE9B,MAAMiB,WAAW,GAAGA,CAAA,KAAM;IACxB;IACA,IAAI,CAACL,UAAU,EAAE;MACfM,MAAM,CAACC,IAAI,CAAC,yBAAyB,EAAE,QAAQ,CAAC;IAClD;EACF,CAAC;EAED,MAAMC,eAAe,GAAIC,CAAC,IAAK;IAC7BR,aAAa,CAAC,KAAK,CAAC;IACpBE,YAAY,CAAC;MACXL,CAAC,EAAEW,CAAC,CAACC,OAAO,GAAGd,QAAQ,CAACE,CAAC;MACzBC,CAAC,EAAEU,CAAC,CAACE,OAAO,GAAGf,QAAQ,CAACG;IAC1B,CAAC,CAAC;IAEF,MAAMa,eAAe,GAAIH,CAAC,IAAK;MAC7BR,aAAa,CAAC,IAAI,CAAC;MACnB,MAAMY,IAAI,GAAGC,IAAI,CAACC,GAAG,CACnB,CAAC,EACDD,IAAI,CAACE,GAAG,CAACV,MAAM,CAACW,UAAU,GAAG,EAAE,EAAER,CAAC,CAACC,OAAO,GAAGR,SAAS,CAACJ,CAAC,CAC1D,CAAC;MACD,MAAMoB,IAAI,GAAGJ,IAAI,CAACC,GAAG,CACnB,CAAC,EACDD,IAAI,CAACE,GAAG,CAACV,MAAM,CAACa,WAAW,GAAG,EAAE,EAAEV,CAAC,CAACE,OAAO,GAAGT,SAAS,CAACH,CAAC,CAC3D,CAAC;MACDF,WAAW,CAAC;QAAEC,CAAC,EAAEe,IAAI;QAAEd,CAAC,EAAEmB;MAAK,CAAC,CAAC;IACnC,CAAC;IAED,MAAME,aAAa,GAAGA,CAAA,KAAM;MAC1BC,QAAQ,CAACC,mBAAmB,CAAC,WAAW,EAAEV,eAAe,CAAC;MAC1DS,QAAQ,CAACC,mBAAmB,CAAC,SAAS,EAAEF,aAAa,CAAC;MACtD;MACAG,UAAU,CAAC,MAAMtB,aAAa,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC;IAC7C,CAAC;IAEDoB,QAAQ,CAACG,gBAAgB,CAAC,WAAW,EAAEZ,eAAe,CAAC;IACvDS,QAAQ,CAACG,gBAAgB,CAAC,SAAS,EAAEJ,aAAa,CAAC;EACrD,CAAC;EAED,MAAMK,gBAAgB,GAAIhB,CAAC,IAAK;IAC9BR,aAAa,CAAC,KAAK,CAAC;IACpB,MAAMyB,KAAK,GAAGjB,CAAC,CAACkB,OAAO,CAAC,CAAC,CAAC;IAC1BxB,YAAY,CAAC;MACXL,CAAC,EAAE4B,KAAK,CAAChB,OAAO,GAAGd,QAAQ,CAACE,CAAC;MAC7BC,CAAC,EAAE2B,KAAK,CAACf,OAAO,GAAGf,QAAQ,CAACG;IAC9B,CAAC,CAAC;IAEF,MAAM6B,eAAe,GAAInB,CAAC,IAAK;MAC7BA,CAAC,CAACoB,cAAc,CAAC,CAAC;MAClB5B,aAAa,CAAC,IAAI,CAAC;MACnB,MAAMyB,KAAK,GAAGjB,CAAC,CAACkB,OAAO,CAAC,CAAC,CAAC;MAC1B,MAAMd,IAAI,GAAGC,IAAI,CAACC,GAAG,CACnB,CAAC,EACDD,IAAI,CAACE,GAAG,CAACV,MAAM,CAACW,UAAU,GAAG,EAAE,EAAES,KAAK,CAAChB,OAAO,GAAGR,SAAS,CAACJ,CAAC,CAC9D,CAAC;MACD,MAAMoB,IAAI,GAAGJ,IAAI,CAACC,GAAG,CACnB,CAAC,EACDD,IAAI,CAACE,GAAG,CAACV,MAAM,CAACa,WAAW,GAAG,EAAE,EAAEO,KAAK,CAACf,OAAO,GAAGT,SAAS,CAACH,CAAC,CAC/D,CAAC;MACDF,WAAW,CAAC;QAAEC,CAAC,EAAEe,IAAI;QAAEd,CAAC,EAAEmB;MAAK,CAAC,CAAC;IACnC,CAAC;IAED,MAAMY,cAAc,GAAGA,CAAA,KAAM;MAC3BT,QAAQ,CAACC,mBAAmB,CAAC,WAAW,EAAEM,eAAe,CAAC;MAC1DP,QAAQ,CAACC,mBAAmB,CAAC,UAAU,EAAEQ,cAAc,CAAC;MACxD;MACAP,UAAU,CAAC,MAAMtB,aAAa,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC;IAC7C,CAAC;IAEDoB,QAAQ,CAACG,gBAAgB,CAAC,WAAW,EAAEI,eAAe,EAAE;MAAEG,OAAO,EAAE;IAAM,CAAC,CAAC;IAC3EV,QAAQ,CAACG,gBAAgB,CAAC,UAAU,EAAEM,cAAc,CAAC;EACvD,CAAC;EAED,oBACEvC,OAAA;IACEyC,GAAG,EAAE5B,SAAU;IACf6B,SAAS,EAAC,wBAAwB;IAClCC,KAAK,EAAE;MACLC,IAAI,EAAE,GAAGvC,QAAQ,CAACE,CAAC,IAAI;MACvBsC,GAAG,EAAE,GAAGxC,QAAQ,CAACG,CAAC;IACpB,CAAE;IAAAsC,QAAA,GAED3C,WAAW,iBACVH,OAAA;MAAK0C,SAAS,EAAC,2IAA2I;MAAAI,QAAA,GAAC,oCAEzJ,eAAA9C,OAAA;QAAK0C,SAAS,EAAC;MAAiF;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpG,CACN,eACDlD,OAAA;MACEmD,OAAO,EAAErC,WAAY;MACrBsC,YAAY,EAAEA,CAAA,KAAMhD,cAAc,CAAC,IAAI,CAAE;MACzCiD,YAAY,EAAEA,CAAA,KAAMjD,cAAc,CAAC,KAAK,CAAE;MAC1CkD,YAAY,EAAEA,CAAA,KAAMlD,cAAc,CAAC,IAAI,CAAE;MACzCmD,UAAU,EAAEA,CAAA,KAAM;QAChBvB,UAAU,CAAC,MAAM5B,cAAc,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;MAC/C,CAAE;MACFsC,SAAS,EAAC,sQAAsQ;MAChR,cAAW,oCAAoC;MAC/Cc,KAAK,EAAC,oCAAoC;MAAAV,QAAA,eAE1C9C,OAAA;QACEyD,KAAK,EAAC,4BAA4B;QAClCf,SAAS,EAAC,kCAAkC;QAC5CgB,OAAO,EAAC,WAAW;QACnBC,IAAI,EAAC,cAAc;QACnBC,WAAW,EAAC,GAAG;QAAAd,QAAA,eAEf9C,OAAA;UAAM6D,CAAC,EAAC;QAAunB;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/nB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAChD,EAAA,CAtHID,cAAc;AAAA6D,EAAA,GAAd7D,cAAc;AAwHpB,eAAeA,cAAc;AAAC,IAAA6D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
#!/usr/bin/env python3
"""
Script untuk mengecek status semua service UNAND Chatbot
"""

import asyncio
import aiohttp
import subprocess
import psutil
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

BACKEND_URL = os.getenv("BACKEND_URL", "http://localhost:8000")
FRONTEND_URL = "http://localhost:3001"

async def check_backend():
    """Cek status backend FastAPI"""
    print("🔧 Checking Backend FastAPI...")
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{BACKEND_URL}/health", timeout=5) as response:
                if response.status == 200:
                    data = await response.json()
                    status = data.get("status", "Unknown")
                    chunk_count = data.get("chunk_count", 0)
                    print(f"   ✅ Backend OK - Status: {status}, Documents: {chunk_count} chunks")
                    return True
                else:
                    print(f"   ❌ Backend responding but unhealthy: {response.status}")
                    return False
    except asyncio.TimeoutError:
        print("   ❌ Backend timeout - not responding")
        return False
    except Exception as e:
        print(f"   ❌ Backend not accessible: {e}")
        return False

async def check_frontend():
    """Cek status frontend React"""
    print("🎨 Checking Frontend React...")
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(FRONTEND_URL, timeout=5) as response:
                if response.status == 200:
                    print("   ✅ Frontend OK - React app responding")
                    return True
                else:
                    print(f"   ❌ Frontend responding but error: {response.status}")
                    return False
    except asyncio.TimeoutError:
        print("   ❌ Frontend timeout - not responding")
        return False
    except Exception as e:
        print(f"   ❌ Frontend not accessible: {e}")
        return False

def check_ports():
    """Cek port yang digunakan"""
    print("🔌 Checking Ports...")
    
    ports_to_check = {
        8000: "Backend FastAPI",
        3001: "Frontend React"
    }
    
    port_status = {}
    for port, service in ports_to_check.items():
        is_used = False
        for conn in psutil.net_connections():
            if conn.laddr.port == port and conn.status == 'LISTEN':
                is_used = True
                break
        
        if is_used:
            print(f"   ✅ Port {port} ({service}) - ACTIVE")
            port_status[port] = True
        else:
            print(f"   ❌ Port {port} ({service}) - NOT ACTIVE")
            port_status[port] = False
    
    return port_status

def check_processes():
    """Cek process yang berjalan"""
    print("⚙️  Checking Processes...")
    
    processes_found = {
        "uvicorn": False,
        "node": False,
        "python": False
    }
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
            
            # Cek uvicorn (backend)
            if 'uvicorn' in cmdline and 'main:app' in cmdline:
                print(f"   ✅ Backend process found (PID: {proc.info['pid']})")
                processes_found["uvicorn"] = True
            
            # Cek node (frontend)
            elif 'node' in proc.info['name'].lower() and 'react-scripts' in cmdline:
                print(f"   ✅ Frontend process found (PID: {proc.info['pid']})")
                processes_found["node"] = True
            
            # Cek telegram bot
            elif 'python' in proc.info['name'].lower() and 'telegram_bot.py' in cmdline:
                print(f"   ✅ Telegram bot process found (PID: {proc.info['pid']})")
                processes_found["python"] = True
                
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    # Report missing processes
    if not processes_found["uvicorn"]:
        print("   ❌ Backend process not found")
    if not processes_found["node"]:
        print("   ❌ Frontend process not found")
    if not processes_found["python"]:
        print("   ❌ Telegram bot process not found")
    
    return processes_found

def check_environment():
    """Cek environment variables"""
    print("🌍 Checking Environment...")
    
    required_vars = {
        "TELEGRAM_BOT_TOKEN": "Telegram Bot Token",
        "GEMINI_API_KEY": "Google Gemini API Key",
        "DATABASE_URL": "PostgreSQL Database URL"
    }
    
    env_status = {}
    for var, description in required_vars.items():
        value = os.getenv(var)
        if value:
            # Mask sensitive values
            if "TOKEN" in var or "KEY" in var:
                masked_value = value[:10] + "..." if len(value) > 10 else "***"
                print(f"   ✅ {var}: {masked_value}")
            else:
                print(f"   ✅ {var}: {value}")
            env_status[var] = True
        else:
            print(f"   ❌ {var}: NOT SET")
            env_status[var] = False
    
    return env_status

async def test_telegram_bot():
    """Test koneksi telegram bot ke backend"""
    print("🤖 Testing Telegram Bot Connection...")
    
    # Cek apakah bot token valid (basic check)
    token = os.getenv("TELEGRAM_BOT_TOKEN")
    if not token:
        print("   ❌ No Telegram bot token found")
        return False
    
    # Test koneksi ke backend dari perspektif bot
    try:
        async with aiohttp.ClientSession() as session:
            # Test create session
            async with session.post(
                f"{BACKEND_URL}/sessions",
                json={"title": "Status Check"}
            ) as response:
                if response.status == 200:
                    print("   ✅ Bot can create sessions in backend")
                    return True
                else:
                    print(f"   ❌ Bot cannot create sessions: {response.status}")
                    return False
    except Exception as e:
        print(f"   ❌ Bot cannot connect to backend: {e}")
        return False

async def main():
    """Main function untuk cek semua status"""
    print("🎯 UNAND Chatbot System - Status Check")
    print("=" * 50)
    
    # Cek environment
    env_status = check_environment()
    print()
    
    # Cek ports
    port_status = check_ports()
    print()
    
    # Cek processes
    process_status = check_processes()
    print()
    
    # Cek backend
    backend_ok = await check_backend()
    print()
    
    # Cek frontend
    frontend_ok = await check_frontend()
    print()
    
    # Test telegram bot
    telegram_ok = await test_telegram_bot()
    print()
    
    # Summary
    print("=" * 50)
    print("📊 SUMMARY")
    print("=" * 50)
    
    all_good = True
    
    if backend_ok:
        print("✅ Backend FastAPI: RUNNING")
    else:
        print("❌ Backend FastAPI: NOT RUNNING")
        all_good = False
    
    if frontend_ok:
        print("✅ Frontend React: RUNNING")
    else:
        print("❌ Frontend React: NOT RUNNING")
        all_good = False
    
    if telegram_ok:
        print("✅ Telegram Bot: READY")
    else:
        print("❌ Telegram Bot: NOT READY")
        all_good = False
    
    print()
    if all_good:
        print("🎉 All services are running properly!")
        print("   🌐 Website: http://localhost:3001")
        print("   🔧 Backend API: http://localhost:8000")
        print("   🤖 Telegram: @junwar_bot")
    else:
        print("⚠️  Some services need attention!")
        print("   Run: python start_all_services.py")
    
    print("=" * 50)

if __name__ == "__main__":
    asyncio.run(main())

@echo off
echo ========================================
echo    Junwar Bot - Telegram Bot UNAND
echo ========================================
echo.

REM Cek apakah Python terinstall
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python tidak ditemukan. Pastikan Python sudah terinstall.
    pause
    exit /b 1
)

REM Pindah ke direktori backend untuk install dependencies
echo 📦 Installing dependencies...
cd backend
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ Gagal install dependencies
    pause
    exit /b 1
)

REM Kembali ke root directory
cd ..

echo.
echo 🚀 Starting services...
echo.
echo 📋 Pastikan:
echo   - Database PostgreSQL sudah berjalan
echo   - File .env sudah dikonfigurasi
echo.

REM Jalankan start_all.py
python start_all.py

echo.
echo ⏹️  Services stopped
pause

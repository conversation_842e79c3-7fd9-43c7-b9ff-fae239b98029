#!/usr/bin/env python3
"""
Script untuk menghentikan semua service UNAND Chatbot
"""

import psutil
import time
import sys

def find_and_kill_processes():
    """Mencari dan menghentikan semua process terkait"""
    print("🔍 Searching for running services...")
    
    processes_to_kill = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
            
            # Backend FastAPI (uvicorn)
            if 'uvicorn' in cmdline and 'main:app' in cmdline:
                processes_to_kill.append(("Backend FastAPI", proc))
            
            # Frontend React (node)
            elif 'node' in proc.info['name'].lower() and 'react-scripts' in cmdline:
                processes_to_kill.append(("Frontend React", proc))
            
            # Telegram Bot
            elif 'python' in proc.info['name'].lower() and ('telegram_bot.py' in cmdline or 'run_telegram_bot.py' in cmdline):
                processes_to_kill.append(("Telegram Bot", proc))
            
            # Start all services script
            elif 'python' in proc.info['name'].lower() and 'start_all_services.py' in cmdline:
                processes_to_kill.append(("Service Manager", proc))
                
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            continue
    
    if not processes_to_kill:
        print("✅ No running services found")
        return
    
    print(f"Found {len(processes_to_kill)} running services:")
    for name, proc in processes_to_kill:
        print(f"   🔸 {name} (PID: {proc.pid})")
    
    print("\n🛑 Stopping services...")
    
    # Terminate processes gracefully
    for name, proc in processes_to_kill:
        try:
            print(f"   Stopping {name}...")
            proc.terminate()
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            print(f"   ⚠️  Could not terminate {name}")
    
    # Wait for processes to terminate
    print("   Waiting for processes to stop...")
    time.sleep(3)
    
    # Force kill if still running
    still_running = []
    for name, proc in processes_to_kill:
        try:
            if proc.is_running():
                still_running.append((name, proc))
        except psutil.NoSuchProcess:
            continue
    
    if still_running:
        print("   Force killing remaining processes...")
        for name, proc in still_running:
            try:
                print(f"   Force killing {name}...")
                proc.kill()
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                print(f"   ⚠️  Could not force kill {name}")
    
    print("✅ All services stopped")

def kill_by_ports():
    """Menghentikan process yang menggunakan port tertentu"""
    print("\n🔌 Checking ports...")
    
    target_ports = [8000, 3001]  # Backend dan Frontend ports
    
    for port in target_ports:
        for conn in psutil.net_connections():
            if conn.laddr.port == port and conn.status == 'LISTEN':
                try:
                    proc = psutil.Process(conn.pid)
                    print(f"   Killing process on port {port} (PID: {conn.pid})")
                    proc.terminate()
                    time.sleep(1)
                    if proc.is_running():
                        proc.kill()
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    print(f"   ⚠️  Could not kill process on port {port}")

def main():
    print("🛑 UNAND Chatbot System - Stop All Services")
    print("=" * 50)
    
    try:
        # Stop by process name/cmdline
        find_and_kill_processes()
        
        # Stop by ports as backup
        kill_by_ports()
        
        print("\n" + "=" * 50)
        print("✅ All services have been stopped")
        print("   You can now safely start services again")
        print("=" * 50)
        
    except KeyboardInterrupt:
        print("\n⏹️  Stop operation cancelled")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error stopping services: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

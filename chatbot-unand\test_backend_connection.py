#!/usr/bin/env python3
"""
Script untuk testing koneksi ke backend FastAPI
"""

import asyncio
import aiohttp
import json
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

BACKEND_URL = os.getenv("BACKEND_URL", "http://localhost:8000")

async def test_backend():
    """Test koneksi dan fungsi backend"""
    print("🔍 Testing Backend Connection...")
    print(f"Backend URL: {BACKEND_URL}")
    print("=" * 50)
    
    async with aiohttp.ClientSession() as session:
        # Test 1: Health Check
        print("1. Testing health endpoint...")
        try:
            async with session.get(f"{BACKEND_URL}/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"   ✅ Health check OK: {data}")
                else:
                    print(f"   ❌ Health check failed: {response.status}")
                    return False
        except Exception as e:
            print(f"   ❌ Cannot connect to backend: {e}")
            return False
        
        # Test 2: Create Session
        print("\n2. Testing session creation...")
        try:
            async with session.post(
                f"{BACKEND_URL}/sessions",
                json={"title": "Test Session"}
            ) as response:
                if response.status == 200:
                    session_data = await response.json()
                    session_id = session_data["session_id"]
                    print(f"   ✅ Session created: {session_id}")
                else:
                    print(f"   ❌ Session creation failed: {response.status}")
                    return False
        except Exception as e:
            print(f"   ❌ Session creation error: {e}")
            return False
        
        # Test 3: Send Chat Message
        print("\n3. Testing chat endpoint...")
        try:
            test_query = "Apa itu Universitas Andalas?"
            async with session.post(
                f"{BACKEND_URL}/chat",
                json={"query": test_query, "session_id": session_id}
            ) as response:
                if response.status == 200:
                    chat_data = await response.json()
                    print(f"   ✅ Chat response received")
                    print(f"   📝 Query: {test_query}")
                    print(f"   🤖 Response: {chat_data['response'][:100]}...")
                else:
                    error_text = await response.text()
                    print(f"   ❌ Chat failed: {response.status}")
                    print(f"   Error: {error_text}")
                    return False
        except Exception as e:
            print(f"   ❌ Chat error: {e}")
            return False
        
        # Test 4: Get Sessions
        print("\n4. Testing get sessions...")
        try:
            async with session.get(f"{BACKEND_URL}/sessions") as response:
                if response.status == 200:
                    sessions = await response.json()
                    print(f"   ✅ Found {len(sessions)} sessions")
                else:
                    print(f"   ❌ Get sessions failed: {response.status}")
        except Exception as e:
            print(f"   ❌ Get sessions error: {e}")
    
    print("\n" + "=" * 50)
    print("✅ All tests passed! Backend is ready for Telegram bot.")
    return True

async def main():
    success = await test_backend()
    if not success:
        print("\n❌ Backend testing failed!")
        print("Please check:")
        print("  1. Backend is running: python -m uvicorn main:app --reload")
        print("  2. Database is running")
        print("  3. Environment variables are set correctly")
        return 1
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
